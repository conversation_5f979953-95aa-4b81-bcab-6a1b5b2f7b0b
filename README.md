# Vue 3 前端开发框架模板

这是一个基于Vue 3和Vite构建的现代化前端开发框架模板，提供完整的开发环境配置和基础架构。

## 项目概述

本项目是一个通用的Vue 3前端开发框架模板，集成了TypeScript、Element Plus、Vue Router等现代前端开发工具，可以作为新项目的起始模板。

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite 4
- **开发语言**: TypeScript
- **UI组件库**: Element Plus
- **路由管理**: Vue Router 4
- **HTTP客户端**: Axios
- **样式处理**: SCSS
- **状态管理**: VueUse
- **开发工具**: ESLint、Prettier

## 核心特性

- 🚀 基于Vue 3 Composition API的现代化开发体验
- ⚡ Vite构建工具，快速的开发和构建
- 🎨 Element Plus UI组件库，丰富的组件生态
- 📱 响应式设计，支持多种设备
- 🔧 完整的TypeScript支持
- 🎯 模块化的项目结构

## 项目结构

```
vue3-frontend-template/
├── public/         # 静态资源目录
├── src/            # 源代码目录
│   ├── assets/     # 资源文件（图片、样式等）
│   ├── common/     # 通用功能目录
│   │   ├── request/  # HTTP请求封装
│   │   └── utils/    # 工具函数
│   ├── components/ # 公共组件
│   ├── router/     # 路由配置
│   ├── styles/     # 全局样式
│   ├── types/      # TypeScript类型定义
│   ├── views/      # 页面视图组件
│   ├── App.vue     # 根组件
│   └── main.ts     # 入口文件
├── index.html      # HTML模板
├── vite.config.ts  # Vite配置
├── tsconfig.json   # TypeScript配置
└── package.json    # 项目依赖配置
```

## 开发环境设置

### 前提条件

- Node.js (v14或更高版本)
- Yarn或npm

### 安装依赖

```bash
# 使用Yarn
yarn

# 或使用npm
npm install
```

### 启动开发服务器

```bash
# 使用Yarn
yarn dev

# 或使用npm
npm run dev
```

应用将在http://localhost:5173运行（除非端口被占用，则会使用下一个可用端口）。

## 构建与部署

### 构建生产版本

```bash
# 使用Yarn
yarn build

# 或使用npm
npm run build
```

构建后的文件将输出到`dist`目录中。

### 类型检查

```bash
# 使用Yarn
yarn type-check

# 或使用npm
npm run type-check
```

### 预览生产构建

```bash
# 使用Yarn
yarn preview

# 或使用npm
npm run preview
```

## 开发工具推荐

- [VS Code](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar)
- [Chrome DevTools](https://developers.google.com/web/tools/chrome-devtools)

## 浏览器兼容性

- Chrome 80+
- Safari 13+
- Firefox 78+
- Edge 88+

## 快速开始

1. 克隆项目到本地
2. 安装依赖：`npm install` 或 `yarn`
3. 启动开发服务器：`npm run dev` 或 `yarn dev`
4. 开始开发你的应用

## 自定义配置

### 添加新页面
1. 在 `src/views` 目录下创建新的Vue组件
2. 在 `src/router/index.ts` 中添加路由配置

### 添加新组件
1. 在 `src/components` 目录下创建组件
2. 在需要的地方导入并使用

### 样式定制
- 全局样式：修改 `src/assets/main.scss`
- 组件样式：在组件内使用 `<style scoped>`

## 贡献指南

欢迎提交Issue和Pull Request来改进这个模板。

## 许可证

MIT License

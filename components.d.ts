/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BRTCTest: typeof import('./src/components/BRTCTest.vue')['default']
    ExampleCard: typeof import('./src/components/ExampleCard.vue')['default']
    InfiniteScroll: typeof import('./src/components/InfiniteScroll.vue')['default']
    LazyImage: typeof import('./src/components/LazyImage.vue')['default']
    PerformanceMonitor: typeof import('./src/components/PerformanceMonitor.vue')['default']
    PullRefresh: typeof import('./src/components/PullRefresh.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RTMPPlayer: typeof import('./src/components/RTMPPlayer.vue')['default']
    VanIcon: typeof import('vant/es')['Icon']
  }
}

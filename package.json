{"name": "vue3-frontend-template", "private": true, "version": "1.0.0", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "check-config": "node scripts/check-config.js", "setup": "node scripts/check-config.js --help"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^13.5.0", "@vueuse/gesture": "^2.0.0", "axios": "^0.27.2", "core-js": "^3.8.3", "element-plus": "^2.9.11", "hammerjs": "^2.0.8", "socket.io-client": "^4.8.1", "vant": "^4.9.21", "vue": "^3.2.25", "vue-router": "^4.0.3"}, "devDependencies": {"@types/node": "^18.15.11", "@vitejs/plugin-vue": "^4.1.0", "sass": "^1.87.0", "terser": "^5.43.1", "typescript": "^5.0.2", "unplugin-vue-components": "^28.8.0", "vite": "^4.3.0", "vue-tsc": "^2.2.10"}}
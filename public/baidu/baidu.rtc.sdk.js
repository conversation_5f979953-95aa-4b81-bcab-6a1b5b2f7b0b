//https://cloud.baidu.com/doc/RTC/index.html#WEBSDKV0.7.1
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).adapter=e()}}(function(){return function a(o,s,c){function d(t,e){if(!s[t]){if(!o[t]){var r="function"==typeof require&&require;if(!e&&r)return r(t,!0);if(p)return p(t,!0);var n=new Error("Cannot find module '"+t+"'");throw n.code="MODULE_NOT_FOUND",n}var i=s[t]={exports:{}};o[t][0].call(i.exports,function(e){return d(o[t][1][e]||e)},i,i.exports,a,o,s,c)}return s[t].exports}for(var p="function"==typeof require&&require,e=0;e<c.length;e++)d(c[e]);return d}({1:[function(e,t,r){"use strict";var n=(0,e("./adapter_factory.js").adapterFactory)({window:window});t.exports=n},{"./adapter_factory.js":2}],2:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.adapterFactory=function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).window,t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimEdge:!0,shimSafari:!0},r=a.log,n=a.detectBrowser(e),i={browserDetails:n,commonShim:p,extractVersion:a.extractVersion,disableLog:a.disableLog,disableWarnings:a.disableWarnings};switch(n.browser){case"chrome":if(!o||!o.shimPeerConnection||!t.shimChrome)return r("Chrome shim is not included in this adapter release."),i;r("adapter.js shimming chrome."),(i.browserShim=o).shimGetUserMedia(e),o.shimMediaStream(e),o.shimPeerConnection(e),o.shimOnTrack(e),o.shimAddTrackRemoveTrack(e),o.shimGetSendersWithDtmf(e),o.shimGetStats(e),o.shimSenderReceiverGetStats(e),o.fixNegotiationNeeded(e),p.shimRTCIceCandidate(e),p.shimConnectionState(e),p.shimMaxMessageSize(e),p.shimSendThrowTypeError(e),p.removeAllowExtmapMixed(e);break;case"firefox":if(!c||!c.shimPeerConnection||!t.shimFirefox)return r("Firefox shim is not included in this adapter release."),i;r("adapter.js shimming firefox."),(i.browserShim=c).shimGetUserMedia(e),c.shimPeerConnection(e),c.shimOnTrack(e),c.shimRemoveStream(e),c.shimSenderGetStats(e),c.shimReceiverGetStats(e),c.shimRTCDataChannel(e),c.shimAddTransceiver(e),c.shimCreateOffer(e),c.shimCreateAnswer(e),p.shimRTCIceCandidate(e),p.shimConnectionState(e),p.shimMaxMessageSize(e),p.shimSendThrowTypeError(e);break;case"edge":if(!s||!s.shimPeerConnection||!t.shimEdge)return r("MS edge shim is not included in this adapter release."),i;r("adapter.js shimming edge."),(i.browserShim=s).shimGetUserMedia(e),s.shimGetDisplayMedia(e),s.shimPeerConnection(e),s.shimReplaceTrack(e),p.shimMaxMessageSize(e),p.shimSendThrowTypeError(e);break;case"safari":if(!d||!t.shimSafari)return r("Safari shim is not included in this adapter release."),i;r("adapter.js shimming safari."),(i.browserShim=d).shimRTCIceServerUrls(e),d.shimCreateOfferLegacy(e),d.shimCallbacksAPI(e),d.shimLocalStreamsAPI(e),d.shimRemoteStreamsAPI(e),d.shimTrackEventTransceiver(e),d.shimGetUserMedia(e),p.shimRTCIceCandidate(e),p.shimMaxMessageSize(e),p.shimSendThrowTypeError(e),p.removeAllowExtmapMixed(e);break;default:r("Unsupported browser!")}return i};var a=n(e("./utils")),o=n(e("./chrome/chrome_shim")),s=n(e("./edge/edge_shim")),c=n(e("./firefox/firefox_shim")),d=n(e("./safari/safari_shim")),p=n(e("./common_shim"));function n(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}},{"./chrome/chrome_shim":3,"./common_shim":6,"./edge/edge_shim":7,"./firefox/firefox_shim":11,"./safari/safari_shim":14,"./utils":15}],3:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return n.shimGetUserMedia}});var i=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return i.shimGetDisplayMedia}}),r.shimMediaStream=function(e){e.MediaStream=e.MediaStream||e.webkitMediaStream},r.shimOnTrack=function(a){if("object"!==(void 0===a?"undefined":s(a))||!a.RTCPeerConnection||"ontrack"in a.RTCPeerConnection.prototype)c.wrapPeerConnectionEvent(a,"track",function(e){return e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e});else{Object.defineProperty(a.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});var e=a.RTCPeerConnection.prototype.setRemoteDescription;a.RTCPeerConnection.prototype.setRemoteDescription=function(){var i=this;return this._ontrackpoly||(this._ontrackpoly=function(n){n.stream.addEventListener("addtrack",function(t){var e=void 0;e=a.RTCPeerConnection.prototype.getReceivers?i.getReceivers().find(function(e){return e.track&&e.track.id===t.track.id}):{track:t.track};var r=new Event("track");r.track=t.track,r.receiver=e,r.transceiver={receiver:e},r.streams=[n.stream],i.dispatchEvent(r)}),n.stream.getTracks().forEach(function(t){var e=void 0;e=a.RTCPeerConnection.prototype.getReceivers?i.getReceivers().find(function(e){return e.track&&e.track.id===t.id}):{track:t};var r=new Event("track");r.track=t,r.receiver=e,r.transceiver={receiver:e},r.streams=[n.stream],i.dispatchEvent(r)})},this.addEventListener("addstream",this._ontrackpoly)),e.apply(this,arguments)}}},r.shimGetSendersWithDtmf=function(e){if("object"===(void 0===e?"undefined":s(e))&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){var n=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};var i=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,t){var r=i.apply(this,arguments);return r||(r=n(this,e),this._senders.push(r)),r};var r=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){r.apply(this,arguments);var t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}var a=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var t=this;this._senders=this._senders||[],a.apply(this,[e]),e.getTracks().forEach(function(e){t._senders.push(n(t,e))})};var t=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){var r=this;this._senders=this._senders||[],t.apply(this,[e]),e.getTracks().forEach(function(t){var e=r._senders.find(function(e){return e.track===t});e&&r._senders.splice(r._senders.indexOf(e),1)})}}else if("object"===(void 0===e?"undefined":s(e))&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){var o=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=o.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}},r.shimGetStats=function(e){if(!e.RTCPeerConnection)return;var s=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){var r=this,e=Array.prototype.slice.call(arguments),t=e[0],n=e[1],i=e[2];if(0<arguments.length&&"function"==typeof t)return s.apply(this,arguments);if(0===s.length&&(0===arguments.length||"function"!=typeof t))return s.apply(this,[]);function a(e){var n={};return e.result().forEach(function(t){var r={id:t.id,timestamp:t.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[t.type]||t.type};t.names().forEach(function(e){r[e]=t.stat(e)}),n[r.id]=r}),n}function o(t){return new Map(Object.keys(t).map(function(e){return[e,t[e]]}))}if(2<=arguments.length){return s.apply(this,[function(e){n(o(a(e)))},t])}return new Promise(function(t,e){s.apply(r,[function(e){t(o(a(e)))},e])}).then(n,i)}},r.shimSenderReceiverGetStats=function(a){if(!("object"===(void 0===a?"undefined":s(a))&&a.RTCPeerConnection&&a.RTCRtpSender&&a.RTCRtpReceiver))return;if(!("getStats"in a.RTCRtpSender.prototype)){var r=a.RTCPeerConnection.prototype.getSenders;r&&(a.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e});var t=a.RTCPeerConnection.prototype.addTrack;t&&(a.RTCPeerConnection.prototype.addTrack=function(){var e=t.apply(this,arguments);return e._pc=this,e}),a.RTCRtpSender.prototype.getStats=function(){var t=this;return this._pc.getStats().then(function(e){return c.filterStats(e,t.track,!0)})}}if(!("getStats"in a.RTCRtpReceiver.prototype)){var n=a.RTCPeerConnection.prototype.getReceivers;n&&(a.RTCPeerConnection.prototype.getReceivers=function(){var t=this,e=n.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e}),c.wrapPeerConnectionEvent(a,"track",function(e){return e.receiver._pc=e.srcElement,e}),a.RTCRtpReceiver.prototype.getStats=function(){var t=this;return this._pc.getStats().then(function(e){return c.filterStats(e,t.track,!1)})}}if(!("getStats"in a.RTCRtpSender.prototype&&"getStats"in a.RTCRtpReceiver.prototype))return;var o=a.RTCPeerConnection.prototype.getStats;a.RTCPeerConnection.prototype.getStats=function(e){if(0<arguments.length&&e instanceof a.MediaStreamTrack){var t=e,r=void 0,n=void 0,i=void 0;return this.getSenders().forEach(function(e){e.track===t&&(r?i=!0:r=e)}),this.getReceivers().forEach(function(e){return e.track===t&&(n?i=!0:n=e),e.track===t}),i||r&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):r?r.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return o.apply(this,arguments)}},r.shimAddTrackRemoveTrackWithNative=p,r.shimAddTrackRemoveTrack=function(o){if(!o.RTCPeerConnection)return;var e=c.detectBrowser(o);if(o.RTCPeerConnection.prototype.addTrack&&65<=e.version)return p(o);var r=o.RTCPeerConnection.prototype.getLocalStreams;o.RTCPeerConnection.prototype.getLocalStreams=function(){var t=this,e=r.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map(function(e){return t._reverseStreams[e.id]})};var n=o.RTCPeerConnection.prototype.addStream;o.RTCPeerConnection.prototype.addStream=function(e){var r=this;if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},e.getTracks().forEach(function(t){if(r.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError")}),!this._reverseStreams[e.id]){var t=new o.MediaStream(e.getTracks());this._streams[e.id]=t,this._reverseStreams[t.id]=e,e=t}n.apply(this,[e])};var t=o.RTCPeerConnection.prototype.removeStream;function i(n,e){var i=e.sdp;return Object.keys(n._reverseStreams||[]).forEach(function(e){var t=n._reverseStreams[e],r=n._streams[t.id];i=i.replace(new RegExp(r.id,"g"),t.id)}),new RTCSessionDescription({type:e.type,sdp:i})}o.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},o.RTCPeerConnection.prototype.addTrack=function(t,e){var r=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");var n=[].slice.call(arguments,1);if(1!==n.length||!n[0].getTracks().find(function(e){return e===t}))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};var i=this._streams[e.id];if(i)i.addTrack(t),Promise.resolve().then(function(){r.dispatchEvent(new Event("negotiationneeded"))});else{var a=new o.MediaStream([t]);this._streams[e.id]=a,this._reverseStreams[a.id]=e,this.addStream(a)}return this.getSenders().find(function(e){return e.track===t})},["createOffer","createAnswer"].forEach(function(e){var t=o.RTCPeerConnection.prototype[e],r=d({},e,function(){var r=this,n=arguments;return arguments.length&&"function"==typeof arguments[0]?t.apply(this,[function(e){var t=i(r,e);n[0].apply(null,[t])},function(e){n[1]&&n[1].apply(null,e)},arguments[2]]):t.apply(this,arguments).then(function(e){return i(r,e)})});o.RTCPeerConnection.prototype[e]=r[e]});var a=o.RTCPeerConnection.prototype.setLocalDescription;o.RTCPeerConnection.prototype.setLocalDescription=function(e){return arguments.length&&e.type&&(e=function(n,e){var i=e.sdp;return Object.keys(n._reverseStreams||[]).forEach(function(e){var t=n._reverseStreams[e],r=n._streams[t.id];i=i.replace(new RegExp(t.id,"g"),r.id)}),new RTCSessionDescription({type:e.type,sdp:i})}(this,e)),a.apply(this,arguments)};var s=Object.getOwnPropertyDescriptor(o.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(o.RTCPeerConnection.prototype,"localDescription",{get:function(){var e=s.get.apply(this);return""===e.type?e:i(this,e)}}),o.RTCPeerConnection.prototype.removeTrack=function(t){var r=this;if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!t._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(t._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");this._streams=this._streams||{};var n=void 0;Object.keys(this._streams).forEach(function(e){r._streams[e].getTracks().find(function(e){return t.track===e})&&(n=r._streams[e])}),n&&(1===n.getTracks().length?this.removeStream(this._reverseStreams[n.id]):n.removeTrack(t.track),this.dispatchEvent(new Event("negotiationneeded")))}},r.shimPeerConnection=function(n){var r=c.detectBrowser(n);!n.RTCPeerConnection&&n.webkitRTCPeerConnection&&(n.RTCPeerConnection=n.webkitRTCPeerConnection);if(!n.RTCPeerConnection)return;r.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(e){var t=n.RTCPeerConnection.prototype[e],r=d({},e,function(){return arguments[0]=new("addIceCandidate"===e?n.RTCIceCandidate:n.RTCSessionDescription)(arguments[0]),t.apply(this,arguments)});n.RTCPeerConnection.prototype[e]=r[e]});var i=n.RTCPeerConnection.prototype.addIceCandidate;n.RTCPeerConnection.prototype.addIceCandidate=function(e,t){return e?r.version<78&&e&&""===e.candidate?Promise.resolve():i.apply(this,arguments):(t&&t.apply(null),Promise.resolve())}},r.fixNegotiationNeeded=function(e){c.wrapPeerConnectionEvent(e,"negotiationneeded",function(e){if("stable"===e.target.signalingState)return e})};var c=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils.js"));function d(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){var t=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map(function(e){return t._shimmedLocalStreams[e][0]})};var n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,t){if(!t)return n.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};var r=n.apply(this,arguments);return this._shimmedLocalStreams[t.id]?-1===this._shimmedLocalStreams[t.id].indexOf(r)&&this._shimmedLocalStreams[t.id].push(r):this._shimmedLocalStreams[t.id]=[t,r],r};var i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){var r=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach(function(t){if(r.getSenders().find(function(e){return e.track===t}))throw new DOMException("Track already exists.","InvalidAccessError")});var t=this.getSenders();i.apply(this,arguments);var n=this.getSenders().filter(function(e){return-1===t.indexOf(e)});this._shimmedLocalStreams[e.id]=[e].concat(n)};var t=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],t.apply(this,arguments)};var a=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(r){var n=this;return this._shimmedLocalStreams=this._shimmedLocalStreams||{},r&&Object.keys(this._shimmedLocalStreams).forEach(function(e){var t=n._shimmedLocalStreams[e].indexOf(r);-1!==t&&n._shimmedLocalStreams[e].splice(t,1),1===n._shimmedLocalStreams[e].length&&delete n._shimmedLocalStreams[e]}),a.apply(this,arguments)}}},{"../utils.js":15,"./getdisplaymedia":4,"./getusermedia":5}],4:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(a,e){if(a.navigator.mediaDevices&&"getDisplayMedia"in a.navigator.mediaDevices)return;if(!a.navigator.mediaDevices)return;if("function"!=typeof e)return void console.error("shimGetDisplayMedia: getSourceId argument is not a function");a.navigator.mediaDevices.getDisplayMedia=function(i){return e(i).then(function(e){var t=i.video&&i.video.width,r=i.video&&i.video.height,n=i.video&&i.video.frameRate;return i.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:e,maxFrameRate:n||3}},t&&(i.video.mandatory.maxWidth=t),r&&(i.video.mandatory.maxHeight=r),a.navigator.mediaDevices.getUserMedia(i)})}}},{}],5:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var o=e&&e.navigator;if(!o.mediaDevices)return;function s(i){if("object"!==(void 0===i?"undefined":d(i))||i.mandatory||i.optional)return i;var a={};return Object.keys(i).forEach(function(t){if("require"!==t&&"advanced"!==t&&"mediaSource"!==t){var r="object"===d(i[t])?i[t]:{ideal:i[t]};void 0!==r.exact&&"number"==typeof r.exact&&(r.min=r.max=r.exact);var n=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==r.ideal){a.optional=a.optional||[];var e={};"number"==typeof r.ideal?(e[n("min",t)]=r.ideal,a.optional.push(e),(e={})[n("max",t)]=r.ideal):e[n("",t)]=r.ideal,a.optional.push(e)}void 0!==r.exact&&"number"!=typeof r.exact?(a.mandatory=a.mandatory||{},a.mandatory[n("",t)]=r.exact):["min","max"].forEach(function(e){void 0!==r[e]&&(a.mandatory=a.mandatory||{},a.mandatory[n(e,t)]=r[e])})}}),i.advanced&&(a.optional=(a.optional||[]).concat(i.advanced)),a}function n(r,n){if(61<=c.version)return n(r);if((r=JSON.parse(JSON.stringify(r)))&&"object"===d(r.audio)){var e=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])};e((r=JSON.parse(JSON.stringify(r))).audio,"autoGainControl","googAutoGainControl"),e(r.audio,"noiseSuppression","googNoiseSuppression"),r.audio=s(r.audio)}if(r&&"object"===d(r.video)){var i=r.video.facingMode;i=i&&("object"===(void 0===i?"undefined":d(i))?i:{ideal:i});var t=c.version<66;if(i&&("user"===i.exact||"environment"===i.exact||"user"===i.ideal||"environment"===i.ideal)&&(!o.mediaDevices.getSupportedConstraints||!o.mediaDevices.getSupportedConstraints().facingMode||t)){delete r.video.facingMode;var a=void 0;if("environment"===i.exact||"environment"===i.ideal?a=["back","rear"]:"user"!==i.exact&&"user"!==i.ideal||(a=["front"]),a)return o.mediaDevices.enumerateDevices().then(function(e){var t=(e=e.filter(function(e){return"videoinput"===e.kind})).find(function(t){return a.some(function(e){return t.label.toLowerCase().includes(e)})});return!t&&e.length&&a.includes("back")&&(t=e[e.length-1]),t&&(r.video.deviceId=i.exact?{exact:t.deviceId}:{ideal:t.deviceId}),r.video=s(r.video),p("chrome: "+JSON.stringify(r)),n(r)})}r.video=s(r.video)}return p("chrome: "+JSON.stringify(r)),n(r)}function i(e){return 64<=c.version?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}}var c=a.detectBrowser(e);if(o.getUserMedia=function(e,t,r){n(e,function(e){o.webkitGetUserMedia(e,t,function(e){if(r){r(i(e))}})})}.bind(o),o.mediaDevices.getUserMedia){var r=o.mediaDevices.getUserMedia.bind(o.mediaDevices);o.mediaDevices.getUserMedia=function(e){return n(e,function(t){return r(t).then(function(e){if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach(function(e){e.stop()}),new DOMException("","NotFoundError");return e},function(e){return Promise.reject(i(e))})})}}};var a=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils.js"));var p=a.log},{"../utils.js":15}],6:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimRTCIceCandidate=function(t){if(!t.RTCIceCandidate||t.RTCIceCandidate&&"foundation"in t.RTCIceCandidate.prototype)return;var i=t.RTCIceCandidate;t.RTCIceCandidate=function(e){if("object"===(void 0===e?"undefined":a(e))&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substr(2)),e.candidate&&e.candidate.length){var t=new i(e),r=c.default.parseCandidate(e.candidate),n=Object.assign(t,r);return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new i(e)},t.RTCIceCandidate.prototype=i.prototype,d.wrapPeerConnectionEvent(t,"icecandidate",function(e){return e.candidate&&Object.defineProperty(e,"candidate",{value:new t.RTCIceCandidate(e.candidate),writable:"false"}),e})},r.shimMaxMessageSize=function(e){if(!e.RTCPeerConnection)return;var o=d.detectBrowser(e);"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp}});var s=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(e){this._sctp=null,"chrome"===o.browser&&76<=o.version&&("plan-b"===this.getConfiguration().sdpSemantics&&Object.defineProperty(this,"sctp",{get:function(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0}));if(function(e){if(!e||!e.sdp)return!1;var t=c.default.splitSections(e.sdp);return t.shift(),t.some(function(e){var t=c.default.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")})}(e)){var t=function(e){var t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;var r=parseInt(t[1],10);return r!=r?-1:r}(e),r=function(e){var t=65536;return"firefox"===o.browser&&(t=o.version<57?-1===e?16384:2147483637:o.version<60?57===o.version?65535:65536:2147483637),t}(t),n=function(e,t){var r=65536;"firefox"===o.browser&&57===o.version&&(r=65535);var n=c.default.matchPrefix(e.sdp,"a=max-message-size:");return 0<n.length?r=parseInt(n[0].substr(19),10):"firefox"===o.browser&&-1!==t&&(r=2147483637),r}(e,t),i=void 0;i=0===r&&0===n?Number.POSITIVE_INFINITY:0===r||0===n?Math.max(r,n):Math.min(r,n);var a={};Object.defineProperty(a,"maxMessageSize",{get:function(){return i}}),this._sctp=a}return s.apply(this,arguments)}},r.shimSendThrowTypeError=function(e){if(!(e.RTCPeerConnection&&"createDataChannel"in e.RTCPeerConnection.prototype))return;function t(n,i){var a=n.send;n.send=function(e){var t=e,r=t.length||t.size||t.byteLength;if("open"===n.readyState&&i.sctp&&r>i.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+i.sctp.maxMessageSize+" bytes)");return a.apply(n,arguments)}}var r=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){var e=r.apply(this,arguments);return t(e,this),e},d.wrapPeerConnectionEvent(e,"datachannel",function(e){return t(e.channel,e.target),e})},r.shimConnectionState=function(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;var r=e.RTCPeerConnection.prototype;Object.defineProperty(r,"connectionState",{get:function(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(r,"onconnectionstatechange",{get:function(){return this._onconnectionstatechange||null},set:function(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach(function(e){var t=r[e];r[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=function(e){var t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;var r=new Event("connectionstatechange",e);t.dispatchEvent(r)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),t.apply(this,arguments)}})},r.removeAllowExtmapMixed=function(e){if(!e.RTCPeerConnection)return;var t=d.detectBrowser(e);if("chrome"===t.browser&&71<=t.version)return;var r=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(e){return e&&e.sdp&&-1!==e.sdp.indexOf("\na=extmap-allow-mixed")&&(e.sdp=e.sdp.split("\n").filter(function(e){return"a=extmap-allow-mixed"!==e.trim()}).join("\n")),r.apply(this,arguments)}};var n,i=e("sdp"),c=(n=i)&&n.__esModule?n:{default:n},d=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("./utils"))},{"./utils":15,sdp:17}],7:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var n=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return n.shimGetUserMedia}});var i=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return i.shimGetDisplayMedia}}),r.shimPeerConnection=function(e){var t=o.detectBrowser(e);if(e.RTCIceGatherer&&(e.RTCIceCandidate||(e.RTCIceCandidate=function(e){return e}),e.RTCSessionDescription||(e.RTCSessionDescription=function(e){return e}),t.version<15025)){var r=Object.getOwnPropertyDescriptor(e.MediaStreamTrack.prototype,"enabled");Object.defineProperty(e.MediaStreamTrack.prototype,"enabled",{set:function(e){r.set.call(this,e);var t=new Event("enabled");t.enabled=e,this.dispatchEvent(t)}})}!e.RTCRtpSender||"dtmf"in e.RTCRtpSender.prototype||Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get:function(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=new e.RTCDtmfSender(this):"video"===this.track.kind&&(this._dtmf=null)),this._dtmf}});e.RTCDtmfSender&&!e.RTCDTMFSender&&(e.RTCDTMFSender=e.RTCDtmfSender);var n=(0,d.default)(e,t.version);e.RTCPeerConnection=function(e){return e&&e.iceServers&&(e.iceServers=(0,s.filterIceServers)(e.iceServers,t.version),o.log("ICE servers after filtering:",e.iceServers)),new n(e)},e.RTCPeerConnection.prototype=n.prototype},r.shimReplaceTrack=function(e){!e.RTCRtpSender||"replaceTrack"in e.RTCRtpSender.prototype||(e.RTCRtpSender.prototype.replaceTrack=e.RTCRtpSender.prototype.setTrack)};var a,o=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils")),s=e("./filtericeservers"),c=e("rtcpeerconnection-shim"),d=(a=c)&&a.__esModule?a:{default:a}},{"../utils":15,"./filtericeservers":8,"./getdisplaymedia":9,"./getusermedia":10,"rtcpeerconnection-shim":16}],8:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.filterIceServers=function(e,t){var n=!1;return(e=JSON.parse(JSON.stringify(e))).filter(function(e){if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&i.deprecated("RTCIceServer.url","RTCIceServer.urls");var r="string"==typeof t;return r&&(t=[t]),t=t.filter(function(e){if(0===e.indexOf("stun:"))return!1;var t=e.startsWith("turn")&&!e.startsWith("turn:[")&&e.includes("transport=udp");return t&&!n?n=!0:t&&!n}),delete e.url,e.urls=r?t[0]:t,!!t.length}})};var i=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils"))},{"../utils":15}],9:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(e){if(!("getDisplayMedia"in e.navigator))return;if(!e.navigator.mediaDevices)return;if(e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices)return;e.navigator.mediaDevices.getDisplayMedia=e.navigator.getDisplayMedia.bind(e.navigator)}},{}],10:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetUserMedia=function(e){var t=e&&e.navigator,r=t.mediaDevices.getUserMedia.bind(t.mediaDevices);t.mediaDevices.getUserMedia=function(e){return r(e).catch(function(e){return Promise.reject(function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}}}(e))})}}},{}],11:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=r.shimGetUserMedia=void 0;var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=e("./getusermedia");Object.defineProperty(r,"shimGetUserMedia",{enumerable:!0,get:function(){return n.shimGetUserMedia}});var i=e("./getdisplaymedia");Object.defineProperty(r,"shimGetDisplayMedia",{enumerable:!0,get:function(){return i.shimGetDisplayMedia}}),r.shimOnTrack=function(e){"object"===(void 0===e?"undefined":c(e))&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimPeerConnection=function(n){var i=s.detectBrowser(n);if("object"!==(void 0===n?"undefined":c(n))||!n.RTCPeerConnection&&!n.mozRTCPeerConnection)return;!n.RTCPeerConnection&&n.mozRTCPeerConnection&&(n.RTCPeerConnection=n.mozRTCPeerConnection);i.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(e){var t=n.RTCPeerConnection.prototype[e],r=function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r;return e}({},e,function(){return arguments[0]=new("addIceCandidate"===e?n.RTCIceCandidate:n.RTCSessionDescription)(arguments[0]),t.apply(this,arguments)});n.RTCPeerConnection.prototype[e]=r[e]});if(i.version<68){var r=n.RTCPeerConnection.prototype.addIceCandidate;n.RTCPeerConnection.prototype.addIceCandidate=function(e,t){return e?e&&""===e.candidate?Promise.resolve():r.apply(this,arguments):(t&&t.apply(null),Promise.resolve())}}var a={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},o=n.RTCPeerConnection.prototype.getStats;n.RTCPeerConnection.prototype.getStats=function(){var e=Array.prototype.slice.call(arguments),t=e[0],n=e[1],r=e[2];return o.apply(this,[t||null]).then(function(r){if(i.version<53&&!n)try{r.forEach(function(e){e.type=a[e.type]||e.type})}catch(e){if("TypeError"!==e.name)throw e;r.forEach(function(e,t){r.set(t,Object.assign({},e,{type:a[e.type]||e.type}))})}return r}).then(n,r)}},r.shimSenderGetStats=function(e){if("object"!==(void 0===e?"undefined":c(e))||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;var r=e.RTCPeerConnection.prototype.getSenders;r&&(e.RTCPeerConnection.prototype.getSenders=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e});var t=e.RTCPeerConnection.prototype.addTrack;t&&(e.RTCPeerConnection.prototype.addTrack=function(){var e=t.apply(this,arguments);return e._pc=this,e});e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}},r.shimReceiverGetStats=function(e){if("object"!==(void 0===e?"undefined":c(e))||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;var r=e.RTCPeerConnection.prototype.getReceivers;r&&(e.RTCPeerConnection.prototype.getReceivers=function(){var t=this,e=r.apply(this,[]);return e.forEach(function(e){return e._pc=t}),e});s.wrapPeerConnectionEvent(e,"track",function(e){return e.receiver._pc=e.srcElement,e}),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}},r.shimRemoveStream=function(e){if(!e.RTCPeerConnection||"removeStream"in e.RTCPeerConnection.prototype)return;e.RTCPeerConnection.prototype.removeStream=function(t){var r=this;s.deprecated("removeStream","removeTrack"),this.getSenders().forEach(function(e){e.track&&t.getTracks().includes(e.track)&&r.removeTrack(e)})}},r.shimRTCDataChannel=function(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)},r.shimAddTransceiver=function(e){if("object"!==(void 0===e?"undefined":c(e))||!e.RTCPeerConnection)return;var s=e.RTCPeerConnection.prototype.addTransceiver;s&&(e.RTCPeerConnection.prototype.addTransceiver=function(e,t){this.setParametersPromises=[];var r=t,n=r&&"sendEncodings"in r;n&&r.sendEncodings.forEach(function(e){if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(1<=parseFloat(e.scaleResolutionDownBy)))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(0<=parseFloat(e.maxFramerate)))throw new RangeError("max_framerate must be >= 0.0")});var i=s.apply(this,arguments);if(n){var a=i.sender,o=a.getParameters();"encodings"in o||(o.encodings=r.sendEncodings,this.setParametersPromises.push(a.setParameters(o).catch(function(){})))}return i})},r.shimCreateOffer=function(e){if("object"!==(void 0===e?"undefined":c(e))||!e.RTCPeerConnection)return;var r=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){var e=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return r.apply(e,t)}).finally(function(){e.setParametersPromises=[]}):r.apply(this,arguments)}},r.shimCreateAnswer=function(e){if("object"!==(void 0===e?"undefined":c(e))||!e.RTCPeerConnection)return;var r=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){var e=this,t=arguments;return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then(function(){return r.apply(e,t)}).finally(function(){e.setParametersPromises=[]}):r.apply(this,arguments)}};var s=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils"))},{"../utils":15,"./getdisplaymedia":12,"./getusermedia":13}],12:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0}),r.shimGetDisplayMedia=function(r,n){if(r.navigator.mediaDevices&&"getDisplayMedia"in r.navigator.mediaDevices)return;if(!r.navigator.mediaDevices)return;r.navigator.mediaDevices.getDisplayMedia=function(e){if(e&&e.video)return!0===e.video?e.video={mediaSource:n}:e.video.mediaSource=n,r.navigator.mediaDevices.getUserMedia(e);var t=new DOMException("getDisplayMedia without video constraints is undefined");return t.name="NotFoundError",t.code=8,Promise.reject(t)}}},{}],13:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimGetUserMedia=function(e){var t=d.detectBrowser(e),n=e&&e.navigator,r=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,r){d.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,r)},!(55<t.version&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){var i=function(e,t,r){t in e&&!(r in e)&&(e[r]=e[t],delete e[t])},a=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(e){return"object"===(void 0===e?"undefined":c(e))&&"object"===c(e.audio)&&(e=JSON.parse(JSON.stringify(e)),i(e.audio,"autoGainControl","mozAutoGainControl"),i(e.audio,"noiseSuppression","mozNoiseSuppression")),a(e)},r&&r.prototype.getSettings){var o=r.prototype.getSettings;r.prototype.getSettings=function(){var e=o.apply(this,arguments);return i(e,"mozAutoGainControl","autoGainControl"),i(e,"mozNoiseSuppression","noiseSuppression"),e}}if(r&&r.prototype.applyConstraints){var s=r.prototype.applyConstraints;r.prototype.applyConstraints=function(e){return"audio"===this.kind&&"object"===(void 0===e?"undefined":c(e))&&(e=JSON.parse(JSON.stringify(e)),i(e,"autoGainControl","mozAutoGainControl"),i(e,"noiseSuppression","mozNoiseSuppression")),s.apply(this,[e])}}}};var d=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils"))},{"../utils":15}],14:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};r.shimLocalStreamsAPI=function(e){if("object"!==(void 0===e?"undefined":d(e))||!e.RTCPeerConnection)return;"getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams});if(!("addStream"in e.RTCPeerConnection.prototype)){var n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(t){var r=this;this._localStreams||(this._localStreams=[]),this._localStreams.includes(t)||this._localStreams.push(t),t.getAudioTracks().forEach(function(e){return n.call(r,e,t)}),t.getVideoTracks().forEach(function(e){return n.call(r,e,t)})},e.RTCPeerConnection.prototype.addTrack=function(e,t){var r=t;return r&&(this._localStreams?this._localStreams.includes(r)||this._localStreams.push(r):this._localStreams=[r]),n.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){var t=this;this._localStreams||(this._localStreams=[]);var r=this._localStreams.indexOf(e);if(-1!==r){this._localStreams.splice(r,1);var n=e.getTracks();this.getSenders().forEach(function(e){n.includes(e.track)&&t.removeTrack(e)})}})},r.shimRemoteStreamsAPI=function(e){if("object"!==(void 0===e?"undefined":d(e))||!e.RTCPeerConnection)return;"getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]});if(!("onaddstream"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(e){var r=this;this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(function(e){if(r._remoteStreams||(r._remoteStreams=[]),!r._remoteStreams.includes(e)){r._remoteStreams.push(e);var t=new Event("addstream");t.stream=e,r.dispatchEvent(t)}})})}});var t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){var r=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(function(e){if(r._remoteStreams||(r._remoteStreams=[]),!(0<=r._remoteStreams.indexOf(e))){r._remoteStreams.push(e);var t=new Event("addstream");t.stream=e,r.dispatchEvent(t)}})}),t.apply(r,arguments)}}},r.shimCallbacksAPI=function(e){if("object"!==(void 0===e?"undefined":d(e))||!e.RTCPeerConnection)return;var t=e.RTCPeerConnection.prototype,a=t.createOffer,o=t.createAnswer,i=t.setLocalDescription,s=t.setRemoteDescription,c=t.addIceCandidate;t.createOffer=function(e,t,r){var n=2<=arguments.length?r:e,i=a.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i},t.createAnswer=function(e,t,r){var n=2<=arguments.length?r:e,i=o.apply(this,[n]);return t?(i.then(e,t),Promise.resolve()):i};var r=function(e,t,r){var n=i.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n};t.setLocalDescription=r,r=function(e,t,r){var n=s.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.setRemoteDescription=r,r=function(e,t,r){var n=c.apply(this,[e]);return r?(n.then(t,r),Promise.resolve()):n},t.addIceCandidate=r},r.shimGetUserMedia=function(e){var n=e&&e.navigator;if(n.mediaDevices&&n.mediaDevices.getUserMedia){var t=n.mediaDevices,r=t.getUserMedia.bind(t);n.mediaDevices.getUserMedia=function(e){return r(i(e))}}!n.getUserMedia&&n.mediaDevices&&n.mediaDevices.getUserMedia&&(n.getUserMedia=function(e,t,r){n.mediaDevices.getUserMedia(e).then(t,r)}.bind(n))},r.shimConstraints=i,r.shimRTCIceServerUrls=function(e){var a=e.RTCPeerConnection;e.RTCPeerConnection=function(e,t){if(e&&e.iceServers){for(var r=[],n=0;n<e.iceServers.length;n++){var i=e.iceServers[n];!i.hasOwnProperty("urls")&&i.hasOwnProperty("url")?(o.deprecated("RTCIceServer.url","RTCIceServer.urls"),(i=JSON.parse(JSON.stringify(i))).urls=i.url,delete i.url,r.push(i)):r.push(e.iceServers[n])}e.iceServers=r}return new a(e,t)},e.RTCPeerConnection.prototype=a.prototype,"generateCertificate"in e.RTCPeerConnection&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:function(){return a.generateCertificate}})},r.shimTrackEventTransceiver=function(e){"object"===(void 0===e?"undefined":d(e))&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})},r.shimCreateOfferLegacy=function(e){var n=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);var t=this.getTransceivers().find(function(e){return"audio"===e.receiver.track.kind});!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio"),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);var r=this.getTransceivers().find(function(e){return"video"===e.receiver.track.kind});!1===e.offerToReceiveVideo&&r?"sendrecv"===r.direction?r.setDirection?r.setDirection("sendonly"):r.direction="sendonly":"recvonly"===r.direction&&(r.setDirection?r.setDirection("inactive"):r.direction="inactive"):!0!==e.offerToReceiveVideo||r||this.addTransceiver("video")}return n.apply(this,arguments)}};var o=function(e){{if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t.default=e,t}}(e("../utils"));function i(e){return e&&void 0!==e.video?Object.assign({},e,{video:o.compactObject(e.video)}):e}},{"../utils":15}],15:[function(e,t,r){"use strict";Object.defineProperty(r,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.extractVersion=o,r.wrapPeerConnectionEvent=function(e,n,i){if(!e.RTCPeerConnection)return;var t=e.RTCPeerConnection.prototype,a=t.addEventListener;t.addEventListener=function(e,r){if(e!==n)return a.apply(this,arguments);function t(e){var t=i(e);t&&r(t)}return this._eventMap=this._eventMap||{},this._eventMap[r]=t,a.apply(this,[e,t])};var o=t.removeEventListener;t.removeEventListener=function(e,t){if(e!==n||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);var r=this._eventMap[t];return delete this._eventMap[t],o.apply(this,[e,r])},Object.defineProperty(t,"on"+n,{get:function(){return this["_on"+n]},set:function(e){this["_on"+n]&&(this.removeEventListener(n,this["_on"+n]),delete this["_on"+n]),e&&this.addEventListener(n,this["_on"+n]=e)},enumerable:!0,configurable:!0})},r.disableLog=function(e){return"boolean"==typeof e?(i=e)?"adapter.js logging disabled":"adapter.js logging enabled":new Error("Argument type: "+(void 0===e?"undefined":n(e))+". Please use a boolean.")},r.disableWarnings=function(e){return"boolean"==typeof e?(a=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled")):new Error("Argument type: "+(void 0===e?"undefined":n(e))+". Please use a boolean.")},r.log=function(){if("object"===("undefined"==typeof window?"undefined":n(window))){if(i)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}},r.deprecated=function(e,t){if(!a)return;console.warn(e+" is deprecated, please use "+t+" instead.")},r.detectBrowser=function(e){var t=e.navigator,r={browser:null,version:null};if(void 0===e||!e.navigator)return r.browser="Not a browser.",r;if(t.mozGetUserMedia)r.browser="firefox",r.version=o(t.userAgent,/Firefox\/(\d+)\./,1);else if(t.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection&&!e.RTCIceGatherer)r.browser="chrome",r.version=o(t.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else if(t.mediaDevices&&t.userAgent.match(/Edge\/(\d+).(\d+)$/))r.browser="edge",r.version=o(t.userAgent,/Edge\/(\d+).(\d+)$/,2);else{if(!e.RTCPeerConnection||!t.userAgent.match(/AppleWebKit\/(\d+)\./))return r.browser="Not a supported browser.",r;r.browser="safari",r.version=o(t.userAgent,/AppleWebKit\/(\d+)\./,1),r.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return r},r.compactObject=function a(o){if(!c(o))return o;return Object.keys(o).reduce(function(e,t){var r=c(o[t]),n=r?a(o[t]):o[t],i=r&&!Object.keys(n).length;return void 0===n||i?e:Object.assign(e,s({},t,n))},{})},r.walkStats=d,r.filterStats=function(r,t,e){var n=e?"outbound-rtp":"inbound-rtp",i=new Map;if(null===t)return i;var a=[];return r.forEach(function(e){"track"===e.type&&e.trackIdentifier===t.id&&a.push(e)}),a.forEach(function(t){r.forEach(function(e){e.type===n&&e.trackId===t.id&&d(r,e,i)})}),i};var i=!0,a=!0;function o(e,t,r){var n=e.match(t);return n&&n.length>=r&&parseInt(n[r],10)}function c(e){return"[object Object]"===Object.prototype.toString.call(e)}function d(t,r,n){r&&!n.has(r.id)&&(n.set(r.id,r),Object.keys(r).forEach(function(e){e.endsWith("Id")?d(t,t.get(r[e]),n):e.endsWith("Ids")&&r[e].forEach(function(e){d(t,t.get(e),n)})}))}},{}],16:[function(e,t,r){"use strict";var G=e("sdp");function c(e,t,r,n,i){var a=G.writeRtpDescription(e.kind,t);if(a+=G.writeIceParameters(e.iceGatherer.getLocalParameters()),a+=G.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":i||"active"),a+="a=mid:"+e.mid+"\r\n",e.rtpSender&&e.rtpReceiver?a+="a=sendrecv\r\n":e.rtpSender?a+="a=sendonly\r\n":e.rtpReceiver?a+="a=recvonly\r\n":a+="a=inactive\r\n",e.rtpSender){var o=e.rtpSender._initialTrackId||e.rtpSender.track.id;e.rtpSender._initialTrackId=o;var s="msid:"+(n?n.id:"-")+" "+o+"\r\n";a+="a="+s,a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+s,e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+s,a+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return a+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+G.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(a+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+G.localCName+"\r\n"),a}function A(d,p){function u(e,t){e=parseInt(e,10);for(var r=0;r<t.length;r++)if(t[r].payloadType===e||t[r].preferredPayloadType===e)return t[r]}var f={codecs:[],headerExtensions:[],fecMechanisms:[]};return d.codecs.forEach(function(r){for(var e=0;e<p.codecs.length;e++){var t=p.codecs[e];if(r.name.toLowerCase()===t.name.toLowerCase()&&r.clockRate===t.clockRate){if("rtx"===r.name.toLowerCase()&&r.parameters&&t.parameters.apt&&(n=r,i=t,a=d.codecs,o=p.codecs,c=s=void 0,s=u(n.parameters.apt,a),c=u(i.parameters.apt,o),!s||!c||s.name.toLowerCase()!==c.name.toLowerCase()))continue;(t=JSON.parse(JSON.stringify(t))).numChannels=Math.min(r.numChannels,t.numChannels),f.codecs.push(t),t.rtcpFeedback=t.rtcpFeedback.filter(function(e){for(var t=0;t<r.rtcpFeedback.length;t++)if(r.rtcpFeedback[t].type===e.type&&r.rtcpFeedback[t].parameter===e.parameter)return!0;return!1});break}}var n,i,a,o,s,c}),d.headerExtensions.forEach(function(e){for(var t=0;t<p.headerExtensions.length;t++){var r=p.headerExtensions[t];if(e.uri===r.uri){f.headerExtensions.push(r);break}}}),f}function a(e,t,r){return-1!=={offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[t][e].indexOf(r)}function N(e,t){var r=e.getRemoteCandidates().find(function(e){return t.foundation===e.foundation&&t.ip===e.ip&&t.port===e.port&&t.priority===e.priority&&t.protocol===e.protocol&&t.type===e.type});return r||e.addRemoteCandidate(t),!r}function m(e,t){var r=new Error(t);return r.name=e,r.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:void 0,OperationError:void 0}[e],r}t.exports=function(I,j){function L(e,t){t.addTrack(e),t.dispatchEvent(new I.MediaStreamTrackEvent("addtrack",{track:e}))}function i(e,t,r,n){var i=new Event("track");i.track=t,i.receiver=r,i.transceiver={receiver:r},i.streams=n,I.setTimeout(function(){e._dispatchEvent("track",i)})}function n(e){var t=this,r=document.createDocumentFragment();if(["addEventListener","removeEventListener","dispatchEvent"].forEach(function(e){t[e]=r[e].bind(r)}),this.canTrickleIceCandidates=null,this.needNegotiation=!1,this.localStreams=[],this.remoteStreams=[],this._localDescription=null,this._remoteDescription=null,this.signalingState="stable",this.iceConnectionState="new",this.connectionState="new",this.iceGatheringState="new",e=JSON.parse(JSON.stringify(e||{})),this.usingBundle="max-bundle"===e.bundlePolicy,"negotiate"===e.rtcpMuxPolicy)throw m("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported");switch(e.rtcpMuxPolicy||(e.rtcpMuxPolicy="require"),e.iceTransportPolicy){case"all":case"relay":break;default:e.iceTransportPolicy="all"}switch(e.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:e.bundlePolicy="balanced"}if(e.iceServers=function(e,n){var i=!1;return(e=JSON.parse(JSON.stringify(e))).filter(function(e){if(e&&(e.urls||e.url)){var t=e.urls||e.url;e.url&&!e.urls&&console.warn("RTCIceServer.url is deprecated! Use urls instead.");var r="string"==typeof t;return r&&(t=[t]),t=t.filter(function(e){return 0===e.indexOf("turn:")&&-1!==e.indexOf("transport=udp")&&-1===e.indexOf("turn:[")&&!i?i=!0:0===e.indexOf("stun:")&&14393<=n&&-1===e.indexOf("?transport=udp")}),delete e.url,e.urls=r?t[0]:t,!!t.length}})}(e.iceServers||[],j),this._iceGatherers=[],e.iceCandidatePoolSize)for(var n=e.iceCandidatePoolSize;0<n;n--)this._iceGatherers.push(new I.RTCIceGatherer({iceServers:e.iceServers,gatherPolicy:e.iceTransportPolicy}));else e.iceCandidatePoolSize=0;this._config=e,this.transceivers=[],this._sdpSessionId=G.generateSessionId(),this._sdpSessionVersion=0,this._dtlsRole=void 0,this._isClosed=!1}Object.defineProperty(n.prototype,"localDescription",{configurable:!0,get:function(){return this._localDescription}}),Object.defineProperty(n.prototype,"remoteDescription",{configurable:!0,get:function(){return this._remoteDescription}}),n.prototype.onicecandidate=null,n.prototype.onaddstream=null,n.prototype.ontrack=null,n.prototype.onremovestream=null,n.prototype.onsignalingstatechange=null,n.prototype.oniceconnectionstatechange=null,n.prototype.onconnectionstatechange=null,n.prototype.onicegatheringstatechange=null,n.prototype.onnegotiationneeded=null,n.prototype.ondatachannel=null,n.prototype._dispatchEvent=function(e,t){this._isClosed||(this.dispatchEvent(t),"function"==typeof this["on"+e]&&this["on"+e](t))},n.prototype._emitGatheringStateChange=function(){var e=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",e)},n.prototype.getConfiguration=function(){return this._config},n.prototype.getLocalStreams=function(){return this.localStreams},n.prototype.getRemoteStreams=function(){return this.remoteStreams},n.prototype._createTransceiver=function(e,t){var r=0<this.transceivers.length,n={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:e,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:!0};if(this.usingBundle&&r)n.iceTransport=this.transceivers[0].iceTransport,n.dtlsTransport=this.transceivers[0].dtlsTransport;else{var i=this._createIceAndDtlsTransports();n.iceTransport=i.iceTransport,n.dtlsTransport=i.dtlsTransport}return t||this.transceivers.push(n),n},n.prototype.addTrack=function(t,e){if(this._isClosed)throw m("InvalidStateError","Attempted to call addTrack on a closed peerconnection.");var r;if(this.transceivers.find(function(e){return e.track===t}))throw m("InvalidAccessError","Track already exists.");for(var n=0;n<this.transceivers.length;n++)this.transceivers[n].track||this.transceivers[n].kind!==t.kind||(r=this.transceivers[n]);return r=r||this._createTransceiver(t.kind),this._maybeFireNegotiationNeeded(),-1===this.localStreams.indexOf(e)&&this.localStreams.push(e),r.track=t,r.stream=e,r.rtpSender=new I.RTCRtpSender(t,r.dtlsTransport),r.rtpSender},n.prototype.addStream=function(t){var r=this;if(15025<=j)t.getTracks().forEach(function(e){r.addTrack(e,t)});else{var n=t.clone();t.getTracks().forEach(function(e,t){var r=n.getTracks()[t];e.addEventListener("enabled",function(e){r.enabled=e.enabled})}),n.getTracks().forEach(function(e){r.addTrack(e,n)})}},n.prototype.removeTrack=function(t){if(this._isClosed)throw m("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.");if(!(t instanceof I.RTCRtpSender))throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.");var e=this.transceivers.find(function(e){return e.rtpSender===t});if(!e)throw m("InvalidAccessError","Sender was not created by this connection.");var r=e.stream;e.rtpSender.stop(),e.rtpSender=null,e.track=null,e.stream=null,-1===this.transceivers.map(function(e){return e.stream}).indexOf(r)&&-1<this.localStreams.indexOf(r)&&this.localStreams.splice(this.localStreams.indexOf(r),1),this._maybeFireNegotiationNeeded()},n.prototype.removeStream=function(e){var r=this;e.getTracks().forEach(function(t){var e=r.getSenders().find(function(e){return e.track===t});e&&r.removeTrack(e)})},n.prototype.getSenders=function(){return this.transceivers.filter(function(e){return!!e.rtpSender}).map(function(e){return e.rtpSender})},n.prototype.getReceivers=function(){return this.transceivers.filter(function(e){return!!e.rtpReceiver}).map(function(e){return e.rtpReceiver})},n.prototype._createIceGatherer=function(r,e){var n=this;if(e&&0<r)return this.transceivers[0].iceGatherer;if(this._iceGatherers.length)return this._iceGatherers.shift();var i=new I.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});return Object.defineProperty(i,"state",{value:"new",writable:!0}),this.transceivers[r].bufferedCandidateEvents=[],this.transceivers[r].bufferCandidates=function(e){var t=!e.candidate||0===Object.keys(e.candidate).length;i.state=t?"completed":"gathering",null!==n.transceivers[r].bufferedCandidateEvents&&n.transceivers[r].bufferedCandidateEvents.push(e)},i.addEventListener("localcandidate",this.transceivers[r].bufferCandidates),i},n.prototype._gather=function(s,c){var d=this,p=this.transceivers[c].iceGatherer;if(!p.onlocalcandidate){var e=this.transceivers[c].bufferedCandidateEvents;this.transceivers[c].bufferedCandidateEvents=null,p.removeEventListener("localcandidate",this.transceivers[c].bufferCandidates),p.onlocalcandidate=function(e){if(!(d.usingBundle&&0<c)){var t=new Event("icecandidate");t.candidate={sdpMid:s,sdpMLineIndex:c};var r=e.candidate,n=!r||0===Object.keys(r).length;if(n)"new"!==p.state&&"gathering"!==p.state||(p.state="completed");else{"new"===p.state&&(p.state="gathering"),r.component=1,r.ufrag=p.getLocalParameters().usernameFragment;var i=G.writeCandidate(r);t.candidate=Object.assign(t.candidate,G.parseCandidate(i)),t.candidate.candidate=i,t.candidate.toJSON=function(){return{candidate:t.candidate.candidate,sdpMid:t.candidate.sdpMid,sdpMLineIndex:t.candidate.sdpMLineIndex,usernameFragment:t.candidate.usernameFragment}}}var a=G.getMediaSections(d._localDescription.sdp);a[t.candidate.sdpMLineIndex]+=n?"a=end-of-candidates\r\n":"a="+t.candidate.candidate+"\r\n",d._localDescription.sdp=G.getDescription(d._localDescription.sdp)+a.join("");var o=d.transceivers.every(function(e){return e.iceGatherer&&"completed"===e.iceGatherer.state});"gathering"!==d.iceGatheringState&&(d.iceGatheringState="gathering",d._emitGatheringStateChange()),n||d._dispatchEvent("icecandidate",t),o&&(d._dispatchEvent("icecandidate",new Event("icecandidate")),d.iceGatheringState="complete",d._emitGatheringStateChange())}},I.setTimeout(function(){e.forEach(function(e){p.onlocalcandidate(e)})},0)}},n.prototype._createIceAndDtlsTransports=function(){var e=this,t=new I.RTCIceTransport(null);t.onicestatechange=function(){e._updateIceConnectionState(),e._updateConnectionState()};var r=new I.RTCDtlsTransport(t);return r.ondtlsstatechange=function(){e._updateConnectionState()},r.onerror=function(){Object.defineProperty(r,"state",{value:"failed",writable:!0}),e._updateConnectionState()},{iceTransport:t,dtlsTransport:r}},n.prototype._disposeIceAndDtlsTransports=function(e){var t=this.transceivers[e].iceGatherer;t&&(delete t.onlocalcandidate,delete this.transceivers[e].iceGatherer);var r=this.transceivers[e].iceTransport;r&&(delete r.onicestatechange,delete this.transceivers[e].iceTransport);var n=this.transceivers[e].dtlsTransport;n&&(delete n.ondtlsstatechange,delete n.onerror,delete this.transceivers[e].dtlsTransport)},n.prototype._transceive=function(e,t,r){var n=A(e.localCapabilities,e.remoteCapabilities);t&&e.rtpSender&&(n.encodings=e.sendEncodingParameters,n.rtcp={cname:G.localCName,compound:e.rtcpParameters.compound},e.recvEncodingParameters.length&&(n.rtcp.ssrc=e.recvEncodingParameters[0].ssrc),e.rtpSender.send(n)),r&&e.rtpReceiver&&0<n.codecs.length&&("video"===e.kind&&e.recvEncodingParameters&&j<15019&&e.recvEncodingParameters.forEach(function(e){delete e.rtx}),e.recvEncodingParameters.length?n.encodings=e.recvEncodingParameters:n.encodings=[{}],n.rtcp={compound:e.rtcpParameters.compound},e.rtcpParameters.cname&&(n.rtcp.cname=e.rtcpParameters.cname),e.sendEncodingParameters.length&&(n.rtcp.ssrc=e.sendEncodingParameters[0].ssrc),e.rtpReceiver.receive(n))},n.prototype.setLocalDescription=function(e){var t,u,f=this;if(-1===["offer","answer"].indexOf(e.type))return Promise.reject(m("TypeError",'Unsupported type "'+e.type+'"'));if(!a("setLocalDescription",e.type,f.signalingState)||f._isClosed)return Promise.reject(m("InvalidStateError","Can not set local "+e.type+" in state "+f.signalingState));if("offer"===e.type)t=G.splitSections(e.sdp),u=t.shift(),t.forEach(function(e,t){var r=G.parseRtpParameters(e);f.transceivers[t].localCapabilities=r}),f.transceivers.forEach(function(e,t){f._gather(e.mid,t)});else if("answer"===e.type){t=G.splitSections(f._remoteDescription.sdp),u=t.shift();var l=0<G.matchPrefix(u,"a=ice-lite").length;t.forEach(function(e,t){var r=f.transceivers[t],n=r.iceGatherer,i=r.iceTransport,a=r.dtlsTransport,o=r.localCapabilities,s=r.remoteCapabilities;if(!(G.isRejected(e)&&0===G.matchPrefix(e,"a=bundle-only").length)&&!r.rejected){var c=G.getIceParameters(e,u),d=G.getDtlsParameters(e,u);l&&(d.role="server"),f.usingBundle&&0!==t||(f._gather(r.mid,t),"new"===i.state&&i.start(n,c,l?"controlling":"controlled"),"new"===a.state&&a.start(d));var p=A(o,s);f._transceive(r,0<p.codecs.length,!1)}})}return f._localDescription={type:e.type,sdp:e.sdp},"offer"===e.type?f._updateSignalingState("have-local-offer"):f._updateSignalingState("stable"),Promise.resolve()},n.prototype.setRemoteDescription=function(_){var w=this;if(-1===["offer","answer"].indexOf(_.type))return Promise.reject(m("TypeError",'Unsupported type "'+_.type+'"'));if(!a("setRemoteDescription",_.type,w.signalingState)||w._isClosed)return Promise.reject(m("InvalidStateError","Can not set remote "+_.type+" in state "+w.signalingState));var k={};w.remoteStreams.forEach(function(e){k[e.id]=e});var x=[],e=G.splitSections(_.sdp),M=e.shift(),D=0<G.matchPrefix(M,"a=ice-lite").length,O=0<G.matchPrefix(M,"a=group:BUNDLE ").length;w.usingBundle=O;var t=G.matchPrefix(M,"a=ice-options:")[0];return w.canTrickleIceCandidates=!!t&&0<=t.substr(14).split(" ").indexOf("trickle"),e.forEach(function(e,t){var r=G.splitLines(e),n=G.getKind(e),i=G.isRejected(e)&&0===G.matchPrefix(e,"a=bundle-only").length,a=r[0].substr(2).split(" ")[2],o=G.getDirection(e,M),s=G.parseMsid(e),c=G.getMid(e)||G.generateIdentifier();if(i||"application"===n&&("DTLS/SCTP"===a||"UDP/DTLS/SCTP"===a))w.transceivers[t]={mid:c,kind:n,protocol:a,rejected:!0};else{var d,p,u,f,l,m,h,v,y;!i&&w.transceivers[t]&&w.transceivers[t].rejected&&(w.transceivers[t]=w._createTransceiver(n,!0));var g,C,S=G.parseRtpParameters(e);i||(g=G.getIceParameters(e,M),(C=G.getDtlsParameters(e,M)).role="client"),h=G.parseRtpEncodingParameters(e);var T=G.parseRtcpParameters(e),P=0<G.matchPrefix(e,"a=end-of-candidates",M).length,R=G.matchPrefix(e,"a=candidate:").map(function(e){return G.parseCandidate(e)}).filter(function(e){return 1===e.component});if(("offer"===_.type||"answer"===_.type)&&!i&&O&&0<t&&w.transceivers[t]&&(w._disposeIceAndDtlsTransports(t),w.transceivers[t].iceGatherer=w.transceivers[0].iceGatherer,w.transceivers[t].iceTransport=w.transceivers[0].iceTransport,w.transceivers[t].dtlsTransport=w.transceivers[0].dtlsTransport,w.transceivers[t].rtpSender&&w.transceivers[t].rtpSender.setTransport(w.transceivers[0].dtlsTransport),w.transceivers[t].rtpReceiver&&w.transceivers[t].rtpReceiver.setTransport(w.transceivers[0].dtlsTransport)),"offer"!==_.type||i){if("answer"===_.type&&!i){p=(d=w.transceivers[t]).iceGatherer,u=d.iceTransport,f=d.dtlsTransport,l=d.rtpReceiver,m=d.sendEncodingParameters,v=d.localCapabilities,w.transceivers[t].recvEncodingParameters=h,w.transceivers[t].remoteCapabilities=S,w.transceivers[t].rtcpParameters=T,R.length&&"new"===u.state&&(!D&&!P||O&&0!==t?R.forEach(function(e){N(d.iceTransport,e)}):u.setRemoteCandidates(R)),O&&0!==t||("new"===u.state&&u.start(p,g,"controlling"),"new"===f.state&&f.start(C)),!A(d.localCapabilities,d.remoteCapabilities).codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&d.sendEncodingParameters[0].rtx&&delete d.sendEncodingParameters[0].rtx,w._transceive(d,"sendrecv"===o||"recvonly"===o,"sendrecv"===o||"sendonly"===o),!l||"sendrecv"!==o&&"sendonly"!==o?delete d.rtpReceiver:(y=l.track,s?(k[s.stream]||(k[s.stream]=new I.MediaStream),L(y,k[s.stream]),x.push([y,l,k[s.stream]])):(k.default||(k.default=new I.MediaStream),L(y,k.default),x.push([y,l,k.default])))}}else{(d=w.transceivers[t]||w._createTransceiver(n)).mid=c,d.iceGatherer||(d.iceGatherer=w._createIceGatherer(t,O)),R.length&&"new"===d.iceTransport.state&&(!P||O&&0!==t?R.forEach(function(e){N(d.iceTransport,e)}):d.iceTransport.setRemoteCandidates(R)),v=I.RTCRtpReceiver.getCapabilities(n),j<15019&&(v.codecs=v.codecs.filter(function(e){return"rtx"!==e.name})),m=d.sendEncodingParameters||[{ssrc:1001*(2*t+2)}];var b,E=!1;if("sendrecv"===o||"sendonly"===o){if(E=!d.rtpReceiver,l=d.rtpReceiver||new I.RTCRtpReceiver(d.dtlsTransport,n),E)y=l.track,s&&"-"===s.stream||(b=s?(k[s.stream]||(k[s.stream]=new I.MediaStream,Object.defineProperty(k[s.stream],"id",{get:function(){return s.stream}})),Object.defineProperty(y,"id",{get:function(){return s.track}}),k[s.stream]):(k.default||(k.default=new I.MediaStream),k.default)),b&&(L(y,b),d.associatedRemoteMediaStreams.push(b)),x.push([y,l,b])}else d.rtpReceiver&&d.rtpReceiver.track&&(d.associatedRemoteMediaStreams.forEach(function(e){var t=e.getTracks().find(function(e){return e.id===d.rtpReceiver.track.id});t&&function(e,t){t.removeTrack(e),t.dispatchEvent(new I.MediaStreamTrackEvent("removetrack",{track:e}))}(t,e)}),d.associatedRemoteMediaStreams=[]);d.localCapabilities=v,d.remoteCapabilities=S,d.rtpReceiver=l,d.rtcpParameters=T,d.sendEncodingParameters=m,d.recvEncodingParameters=h,w._transceive(w.transceivers[t],!1,E)}}}),void 0===w._dtlsRole&&(w._dtlsRole="offer"===_.type?"active":"passive"),w._remoteDescription={type:_.type,sdp:_.sdp},"offer"===_.type?w._updateSignalingState("have-remote-offer"):w._updateSignalingState("stable"),Object.keys(k).forEach(function(e){var n=k[e];if(n.getTracks().length){if(-1===w.remoteStreams.indexOf(n)){w.remoteStreams.push(n);var t=new Event("addstream");t.stream=n,I.setTimeout(function(){w._dispatchEvent("addstream",t)})}x.forEach(function(e){var t=e[0],r=e[1];n.id===e[2].id&&i(w,t,r,[n])})}}),x.forEach(function(e){e[2]||i(w,e[0],e[1],[])}),I.setTimeout(function(){w&&w.transceivers&&w.transceivers.forEach(function(e){e.iceTransport&&"new"===e.iceTransport.state&&0<e.iceTransport.getRemoteCandidates().length&&(console.warn("Timeout for addRemoteCandidate. Consider sending an end-of-candidates notification"),e.iceTransport.addRemoteCandidate({}))})},4e3),Promise.resolve()},n.prototype.close=function(){this.transceivers.forEach(function(e){e.iceTransport&&e.iceTransport.stop(),e.dtlsTransport&&e.dtlsTransport.stop(),e.rtpSender&&e.rtpSender.stop(),e.rtpReceiver&&e.rtpReceiver.stop()}),this._isClosed=!0,this._updateSignalingState("closed")},n.prototype._updateSignalingState=function(e){this.signalingState=e;var t=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",t)},n.prototype._maybeFireNegotiationNeeded=function(){var t=this;"stable"===this.signalingState&&!0!==this.needNegotiation&&(this.needNegotiation=!0,I.setTimeout(function(){if(t.needNegotiation){t.needNegotiation=!1;var e=new Event("negotiationneeded");t._dispatchEvent("negotiationneeded",e)}},0))},n.prototype._updateIceConnectionState=function(){var e,t={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&!e.rejected&&t[e.iceTransport.state]++}),e="new",0<t.failed?e="failed":0<t.checking?e="checking":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected?e="connected":0<t.completed&&(e="completed"),e!==this.iceConnectionState){this.iceConnectionState=e;var r=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",r)}},n.prototype._updateConnectionState=function(){var e,t={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};if(this.transceivers.forEach(function(e){e.iceTransport&&e.dtlsTransport&&!e.rejected&&(t[e.iceTransport.state]++,t[e.dtlsTransport.state]++)}),t.connected+=t.completed,e="new",0<t.failed?e="failed":0<t.connecting?e="connecting":0<t.disconnected?e="disconnected":0<t.new?e="new":0<t.connected&&(e="connected"),e!==this.connectionState){this.connectionState=e;var r=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",r)}},n.prototype.createOffer=function(){var s=this;if(s._isClosed)return Promise.reject(m("InvalidStateError","Can not call createOffer after close"));var t=s.transceivers.filter(function(e){return"audio"===e.kind}).length,r=s.transceivers.filter(function(e){return"video"===e.kind}).length,e=arguments[0];if(e){if(e.mandatory||e.optional)throw new TypeError("Legacy mandatory/optional constraints not supported.");void 0!==e.offerToReceiveAudio&&(t=!0===e.offerToReceiveAudio?1:!1===e.offerToReceiveAudio?0:e.offerToReceiveAudio),void 0!==e.offerToReceiveVideo&&(r=!0===e.offerToReceiveVideo?1:!1===e.offerToReceiveVideo?0:e.offerToReceiveVideo)}for(s.transceivers.forEach(function(e){"audio"===e.kind?--t<0&&(e.wantReceive=!1):"video"===e.kind&&--r<0&&(e.wantReceive=!1)});0<t||0<r;)0<t&&(s._createTransceiver("audio"),t--),0<r&&(s._createTransceiver("video"),r--);var n=G.writeSessionBoilerplate(s._sdpSessionId,s._sdpSessionVersion++);s.transceivers.forEach(function(e,t){var r=e.track,n=e.kind,i=e.mid||G.generateIdentifier();e.mid=i,e.iceGatherer||(e.iceGatherer=s._createIceGatherer(t,s.usingBundle));var a=I.RTCRtpSender.getCapabilities(n);j<15019&&(a.codecs=a.codecs.filter(function(e){return"rtx"!==e.name})),a.codecs.forEach(function(t){"H264"===t.name&&void 0===t.parameters["level-asymmetry-allowed"]&&(t.parameters["level-asymmetry-allowed"]="1"),e.remoteCapabilities&&e.remoteCapabilities.codecs&&e.remoteCapabilities.codecs.forEach(function(e){t.name.toLowerCase()===e.name.toLowerCase()&&t.clockRate===e.clockRate&&(t.preferredPayloadType=e.payloadType)})}),a.headerExtensions.forEach(function(t){(e.remoteCapabilities&&e.remoteCapabilities.headerExtensions||[]).forEach(function(e){t.uri===e.uri&&(t.id=e.id)})});var o=e.sendEncodingParameters||[{ssrc:1001*(2*t+1)}];r&&15019<=j&&"video"===n&&!o[0].rtx&&(o[0].rtx={ssrc:o[0].ssrc+1}),e.wantReceive&&(e.rtpReceiver=new I.RTCRtpReceiver(e.dtlsTransport,n)),e.localCapabilities=a,e.sendEncodingParameters=o}),"max-compat"!==s._config.bundlePolicy&&(n+="a=group:BUNDLE "+s.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),n+="a=ice-options:trickle\r\n",s.transceivers.forEach(function(e,t){n+=c(e,e.localCapabilities,"offer",e.stream,s._dtlsRole),n+="a=rtcp-rsize\r\n",!e.iceGatherer||"new"===s.iceGatheringState||0!==t&&s.usingBundle||(e.iceGatherer.getLocalCandidates().forEach(function(e){e.component=1,n+="a="+G.writeCandidate(e)+"\r\n"}),"completed"===e.iceGatherer.state&&(n+="a=end-of-candidates\r\n"))});var i=new I.RTCSessionDescription({type:"offer",sdp:n});return Promise.resolve(i)},n.prototype.createAnswer=function(){var i=this;if(i._isClosed)return Promise.reject(m("InvalidStateError","Can not call createAnswer after close"));if("have-remote-offer"!==i.signalingState&&"have-local-pranswer"!==i.signalingState)return Promise.reject(m("InvalidStateError","Can not call createAnswer in signalingState "+i.signalingState));var a=G.writeSessionBoilerplate(i._sdpSessionId,i._sdpSessionVersion++);i.usingBundle&&(a+="a=group:BUNDLE "+i.transceivers.map(function(e){return e.mid}).join(" ")+"\r\n"),a+="a=ice-options:trickle\r\n";var o=G.getMediaSections(i._remoteDescription.sdp).length;i.transceivers.forEach(function(e,t){if(!(o<t+1)){if(e.rejected)return"application"===e.kind?"DTLS/SCTP"===e.protocol?a+="m=application 0 DTLS/SCTP 5000\r\n":a+="m=application 0 "+e.protocol+" webrtc-datachannel\r\n":"audio"===e.kind?a+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\na=rtpmap:0 PCMU/8000\r\n":"video"===e.kind&&(a+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\na=rtpmap:120 VP8/90000\r\n"),void(a+="c=IN IP4 0.0.0.0\r\na=inactive\r\na=mid:"+e.mid+"\r\n");var r;if(e.stream)"audio"===e.kind?r=e.stream.getAudioTracks()[0]:"video"===e.kind&&(r=e.stream.getVideoTracks()[0]),r&&15019<=j&&"video"===e.kind&&!e.sendEncodingParameters[0].rtx&&(e.sendEncodingParameters[0].rtx={ssrc:e.sendEncodingParameters[0].ssrc+1});var n=A(e.localCapabilities,e.remoteCapabilities);!n.codecs.filter(function(e){return"rtx"===e.name.toLowerCase()}).length&&e.sendEncodingParameters[0].rtx&&delete e.sendEncodingParameters[0].rtx,a+=c(e,n,"answer",e.stream,i._dtlsRole),e.rtcpParameters&&e.rtcpParameters.reducedSize&&(a+="a=rtcp-rsize\r\n")}});var e=new I.RTCSessionDescription({type:"answer",sdp:a});return Promise.resolve(e)},n.prototype.addIceCandidate=function(c){var d,p=this;return c&&void 0===c.sdpMLineIndex&&!c.sdpMid?Promise.reject(new TypeError("sdpMLineIndex or sdpMid required")):new Promise(function(e,t){if(!p._remoteDescription)return t(m("InvalidStateError","Can not add ICE candidate without a remote description"));if(c&&""!==c.candidate){var r=c.sdpMLineIndex;if(c.sdpMid)for(var n=0;n<p.transceivers.length;n++)if(p.transceivers[n].mid===c.sdpMid){r=n;break}var i=p.transceivers[r];if(!i)return t(m("OperationError","Can not add ICE candidate"));if(i.rejected)return e();var a=0<Object.keys(c.candidate).length?G.parseCandidate(c.candidate):{};if("tcp"===a.protocol&&(0===a.port||9===a.port))return e();if(a.component&&1!==a.component)return e();if((0===r||0<r&&i.iceTransport!==p.transceivers[0].iceTransport)&&!N(i.iceTransport,a))return t(m("OperationError","Can not add ICE candidate"));var o=c.candidate.trim();0===o.indexOf("a=")&&(o=o.substr(2)),(d=G.getMediaSections(p._remoteDescription.sdp))[r]+="a="+(a.type?o:"end-of-candidates")+"\r\n",p._remoteDescription.sdp=G.getDescription(p._remoteDescription.sdp)+d.join("")}else for(var s=0;s<p.transceivers.length&&(p.transceivers[s].rejected||(p.transceivers[s].iceTransport.addRemoteCandidate({}),(d=G.getMediaSections(p._remoteDescription.sdp))[s]+="a=end-of-candidates\r\n",p._remoteDescription.sdp=G.getDescription(p._remoteDescription.sdp)+d.join(""),!p.usingBundle));s++);e()})},n.prototype.getStats=function(t){if(t&&t instanceof I.MediaStreamTrack){var r=null;if(this.transceivers.forEach(function(e){e.rtpSender&&e.rtpSender.track===t?r=e.rtpSender:e.rtpReceiver&&e.rtpReceiver.track===t&&(r=e.rtpReceiver)}),!r)throw m("InvalidAccessError","Invalid selector.");return r.getStats()}var n=[];return this.transceivers.forEach(function(t){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(e){t[e]&&n.push(t[e].getStats())})}),Promise.all(n).then(function(e){var t=new Map;return e.forEach(function(e){e.forEach(function(e){t.set(e.id,e)})}),t})};["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"].forEach(function(e){var t=I[e];if(t&&t.prototype&&t.prototype.getStats){var r=t.prototype.getStats;t.prototype.getStats=function(){return r.apply(this).then(function(t){var r=new Map;return Object.keys(t).forEach(function(e){t[e].type=function(e){return{inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[e.type]||e.type}(t[e]),r.set(e,t[e])}),r})}}});var e=["createOffer","createAnswer"];return e.forEach(function(e){var r=n.prototype[e];n.prototype[e]=function(){var t=arguments;return"function"==typeof t[0]||"function"==typeof t[1]?r.apply(this,[arguments[2]]).then(function(e){"function"==typeof t[0]&&t[0].apply(null,[e])},function(e){"function"==typeof t[1]&&t[1].apply(null,[e])}):r.apply(this,arguments)}}),(e=["setLocalDescription","setRemoteDescription","addIceCandidate"]).forEach(function(e){var r=n.prototype[e];n.prototype[e]=function(){var t=arguments;return"function"==typeof t[1]||"function"==typeof t[2]?r.apply(this,arguments).then(function(){"function"==typeof t[1]&&t[1].apply(null)},function(e){"function"==typeof t[2]&&t[2].apply(null,[e])}):r.apply(this,arguments)}}),["getStats"].forEach(function(e){var t=n.prototype[e];n.prototype[e]=function(){var e=arguments;return"function"==typeof e[1]?t.apply(this,arguments).then(function(){"function"==typeof e[1]&&e[1].apply(null)}):t.apply(this,arguments)}}),n}},{sdp:17}],17:[function(e,t,r){"use strict";var p={generateIdentifier:function(){return Math.random().toString(36).substr(2,10)}};p.localCName=p.generateIdentifier(),p.splitLines=function(e){return e.trim().split("\n").map(function(e){return e.trim()})},p.splitSections=function(e){return e.split("\nm=").map(function(e,t){return(0<t?"m="+e:e).trim()+"\r\n"})},p.getDescription=function(e){var t=p.splitSections(e);return t&&t[0]},p.getMediaSections=function(e){var t=p.splitSections(e);return t.shift(),t},p.matchPrefix=function(e,t){return p.splitLines(e).filter(function(e){return 0===e.indexOf(t)})},p.parseCandidate=function(e){for(var t,r={foundation:(t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" "))[0],component:parseInt(t[1],10),protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]},n=8;n<t.length;n+=2)switch(t[n]){case"raddr":r.relatedAddress=t[n+1];break;case"rport":r.relatedPort=parseInt(t[n+1],10);break;case"tcptype":r.tcpType=t[n+1];break;case"ufrag":r.ufrag=t[n+1],r.usernameFragment=t[n+1];break;default:r[t[n]]=t[n+1]}return r},p.writeCandidate=function(e){var t=[];t.push(e.foundation),t.push(e.component),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);var r=e.type;return t.push("typ"),t.push(r),"host"!==r&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},p.parseIceOptions=function(e){return e.substr(14).split(" ")},p.parseRtpMap=function(e){var t=e.substr(9).split(" "),r={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),r.name=t[0],r.clockRate=parseInt(t[1],10),r.channels=3===t.length?parseInt(t[2],10):1,r.numChannels=r.channels,r},p.writeRtpMap=function(e){var t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);var r=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==r?"/"+r:"")+"\r\n"},p.parseExtmap=function(e){var t=e.substr(9).split(" ");return{id:parseInt(t[0],10),direction:0<t[0].indexOf("/")?t[0].split("/")[1]:"sendrecv",uri:t[1]}},p.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+"\r\n"},p.parseFmtp=function(e){for(var t,r={},n=e.substr(e.indexOf(" ")+1).split(";"),i=0;i<n.length;i++)r[(t=n[i].trim().split("="))[0].trim()]=t[1];return r},p.writeFmtp=function(t){var e="",r=t.payloadType;if(void 0!==t.preferredPayloadType&&(r=t.preferredPayloadType),t.parameters&&Object.keys(t.parameters).length){var n=[];Object.keys(t.parameters).forEach(function(e){t.parameters[e]?n.push(e+"="+t.parameters[e]):n.push(e)}),e+="a=fmtp:"+r+" "+n.join(";")+"\r\n"}return e},p.parseRtcpFb=function(e){var t=e.substr(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},p.writeRtcpFb=function(e){var t="",r=e.payloadType;return void 0!==e.preferredPayloadType&&(r=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach(function(e){t+="a=rtcp-fb:"+r+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"}),t},p.parseSsrcMedia=function(e){var t=e.indexOf(" "),r={ssrc:parseInt(e.substr(7,t-7),10)},n=e.indexOf(":",t);return-1<n?(r.attribute=e.substr(t+1,n-t-1),r.value=e.substr(n+1)):r.attribute=e.substr(t+1),r},p.parseSsrcGroup=function(e){var t=e.substr(13).split(" ");return{semantics:t.shift(),ssrcs:t.map(function(e){return parseInt(e,10)})}},p.getMid=function(e){var t=p.matchPrefix(e,"a=mid:")[0];if(t)return t.substr(6)},p.parseFingerprint=function(e){var t=e.substr(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1]}},p.getDtlsParameters=function(e,t){return{role:"auto",fingerprints:p.matchPrefix(e+t,"a=fingerprint:").map(p.parseFingerprint)}},p.writeDtlsParameters=function(e,t){var r="a=setup:"+t+"\r\n";return e.fingerprints.forEach(function(e){r+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"}),r},p.getIceParameters=function(e,t){var r=p.splitLines(e);return{usernameFragment:(r=r.concat(p.splitLines(t))).filter(function(e){return 0===e.indexOf("a=ice-ufrag:")})[0].substr(12),password:r.filter(function(e){return 0===e.indexOf("a=ice-pwd:")})[0].substr(10)}},p.writeIceParameters=function(e){return"a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n"},p.parseRtpParameters=function(e){for(var t={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},r=p.splitLines(e)[0].split(" "),n=3;n<r.length;n++){var i=r[n],a=p.matchPrefix(e,"a=rtpmap:"+i+" ")[0];if(a){var o=p.parseRtpMap(a),s=p.matchPrefix(e,"a=fmtp:"+i+" ");switch(o.parameters=s.length?p.parseFmtp(s[0]):{},o.rtcpFeedback=p.matchPrefix(e,"a=rtcp-fb:"+i+" ").map(p.parseRtcpFb),t.codecs.push(o),o.name.toUpperCase()){case"RED":case"ULPFEC":t.fecMechanisms.push(o.name.toUpperCase())}}}return p.matchPrefix(e,"a=extmap:").forEach(function(e){t.headerExtensions.push(p.parseExtmap(e))}),t},p.writeRtpDescription=function(e,t){var r="";r+="m="+e+" ",r+=0<t.codecs.length?"9":"0",r+=" UDP/TLS/RTP/SAVPF ",r+=t.codecs.map(function(e){return void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType}).join(" ")+"\r\n",r+="c=IN IP4 0.0.0.0\r\n",r+="a=rtcp:9 IN IP4 0.0.0.0\r\n",t.codecs.forEach(function(e){r+=p.writeRtpMap(e),r+=p.writeFmtp(e),r+=p.writeRtcpFb(e)});var n=0;return t.codecs.forEach(function(e){e.maxptime>n&&(n=e.maxptime)}),0<n&&(r+="a=maxptime:"+n+"\r\n"),r+="a=rtcp-mux\r\n",t.headerExtensions&&t.headerExtensions.forEach(function(e){r+=p.writeExtmap(e)}),r},p.parseRtpEncodingParameters=function(e){var r,n=[],t=p.parseRtpParameters(e),i=-1!==t.fecMechanisms.indexOf("RED"),a=-1!==t.fecMechanisms.indexOf("ULPFEC"),o=p.matchPrefix(e,"a=ssrc:").map(function(e){return p.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute}),s=0<o.length&&o[0].ssrc,c=p.matchPrefix(e,"a=ssrc-group:FID").map(function(e){return e.substr(17).split(" ").map(function(e){return parseInt(e,10)})});0<c.length&&1<c[0].length&&c[0][0]===s&&(r=c[0][1]),t.codecs.forEach(function(e){if("RTX"===e.name.toUpperCase()&&e.parameters.apt){var t={ssrc:s,codecPayloadType:parseInt(e.parameters.apt,10)};s&&r&&(t.rtx={ssrc:r}),n.push(t),i&&((t=JSON.parse(JSON.stringify(t))).fec={ssrc:s,mechanism:a?"red+ulpfec":"red"},n.push(t))}}),0===n.length&&s&&n.push({ssrc:s});var d=p.matchPrefix(e,"b=");return d.length&&(d=0===d[0].indexOf("b=TIAS:")?parseInt(d[0].substr(7),10):0===d[0].indexOf("b=AS:")?1e3*parseInt(d[0].substr(5),10)*.95-16e3:void 0,n.forEach(function(e){e.maxBitrate=d})),n},p.parseRtcpParameters=function(e){var t={},r=p.matchPrefix(e,"a=ssrc:").map(function(e){return p.parseSsrcMedia(e)}).filter(function(e){return"cname"===e.attribute})[0];r&&(t.cname=r.value,t.ssrc=r.ssrc);var n=p.matchPrefix(e,"a=rtcp-rsize");t.reducedSize=0<n.length,t.compound=0===n.length;var i=p.matchPrefix(e,"a=rtcp-mux");return t.mux=0<i.length,t},p.parseMsid=function(e){var t,r=p.matchPrefix(e,"a=msid:");if(1===r.length)return{stream:(t=r[0].substr(7).split(" "))[0],track:t[1]};var n=p.matchPrefix(e,"a=ssrc:").map(function(e){return p.parseSsrcMedia(e)}).filter(function(e){return"msid"===e.attribute});return 0<n.length?{stream:(t=n[0].value.split(" "))[0],track:t[1]}:void 0},p.parseSctpDescription=function(e){var t,r=p.parseMLine(e),n=p.matchPrefix(e,"a=max-message-size:");0<n.length&&(t=parseInt(n[0].substr(19),10)),isNaN(t)&&(t=65536);var i=p.matchPrefix(e,"a=sctp-port:");if(0<i.length)return{port:parseInt(i[0].substr(12),10),protocol:r.fmt,maxMessageSize:t};if(0<p.matchPrefix(e,"a=sctpmap:").length){var a=p.matchPrefix(e,"a=sctpmap:")[0].substr(10).split(" ");return{port:parseInt(a[0],10),protocol:a[1],maxMessageSize:t}}},p.writeSctpDescription=function(e,t){var r=[];return r="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&r.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),r.join("")},p.generateSessionId=function(){return Math.random().toString().substr(2,21)},p.writeSessionBoilerplate=function(e,t,r){var n=void 0!==t?t:2;return"v=0\r\no="+(r||"thisisadapterortc")+" "+(e||p.generateSessionId())+" "+n+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},p.writeMediaSection=function(e,t,r,n){var i=p.writeRtpDescription(e.kind,t);if(i+=p.writeIceParameters(e.iceGatherer.getLocalParameters()),i+=p.writeDtlsParameters(e.dtlsTransport.getLocalParameters(),"offer"===r?"actpass":"active"),i+="a=mid:"+e.mid+"\r\n",e.direction?i+="a="+e.direction+"\r\n":e.rtpSender&&e.rtpReceiver?i+="a=sendrecv\r\n":e.rtpSender?i+="a=sendonly\r\n":e.rtpReceiver?i+="a=recvonly\r\n":i+="a=inactive\r\n",e.rtpSender){var a="msid:"+n.id+" "+e.rtpSender.track.id+"\r\n";i+="a="+a,i+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" "+a,e.sendEncodingParameters[0].rtx&&(i+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" "+a,i+="a=ssrc-group:FID "+e.sendEncodingParameters[0].ssrc+" "+e.sendEncodingParameters[0].rtx.ssrc+"\r\n")}return i+="a=ssrc:"+e.sendEncodingParameters[0].ssrc+" cname:"+p.localCName+"\r\n",e.rtpSender&&e.sendEncodingParameters[0].rtx&&(i+="a=ssrc:"+e.sendEncodingParameters[0].rtx.ssrc+" cname:"+p.localCName+"\r\n"),i},p.getDirection=function(e,t){for(var r=p.splitLines(e),n=0;n<r.length;n++)switch(r[n]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return r[n].substr(2)}return t?p.getDirection(t):"sendrecv"},p.getKind=function(e){return p.splitLines(e)[0].split(" ")[0].substr(2)},p.isRejected=function(e){return"0"===e.split(" ",2)[1]},p.parseMLine=function(e){var t=p.splitLines(e)[0].substr(2).split(" ");return{kind:t[0],port:parseInt(t[1],10),protocol:t[2],fmt:t.slice(3).join(" ")}},p.parseOLine=function(e){var t=p.matchPrefix(e,"o=")[0].substr(2).split(" ");return{username:t[0],sessionId:t[1],sessionVersion:parseInt(t[2],10),netType:t[3],addressType:t[4],address:t[5]}},p.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;for(var t=p.splitLines(e),r=0;r<t.length;r++)if(t[r].length<2||"="!==t[r].charAt(1))return!1;return!0},"object"==typeof t&&(t.exports=p)},{}]},{},[1])(1)});// http://spin.js.org/#v2.3.2
!function(a,b){"object"==typeof module&&module.exports?module.exports=b():"function"==typeof define&&define.amd?define(b):a.Spinner=b()}(this,function(){"use strict";function a(a,b){var c,d=document.createElement(a||"div");for(c in b)d[c]=b[c];return d}function b(a){for(var b=1,c=arguments.length;c>b;b++)a.appendChild(arguments[b]);return a}function c(a,b,c,d){var e=["opacity",b,~~(100*a),c,d].join("-"),f=.01+c/d*100,g=Math.max(1-(1-a)/b*(100-f),a),h=j.substring(0,j.indexOf("Animation")).toLowerCase(),i=h&&"-"+h+"-"||"";return m[e]||(k.insertRule("@"+i+"keyframes "+e+"{0%{opacity:"+g+"}"+f+"%{opacity:"+a+"}"+(f+.01)+"%{opacity:1}"+(f+b)%100+"%{opacity:"+a+"}100%{opacity:"+g+"}}",k.cssRules.length),m[e]=1),e}function d(a,b){var c,d,e=a.style;if(b=b.charAt(0).toUpperCase()+b.slice(1),void 0!==e[b])return b;for(d=0;d<l.length;d++)if(c=l[d]+b,void 0!==e[c])return c}function e(a,b){for(var c in b)a.style[d(a,c)||c]=b[c];return a}function f(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)void 0===a[d]&&(a[d]=c[d])}return a}function g(a,b){return"string"==typeof a?a:a[b%a.length]}function h(a){this.opts=f(a||{},h.defaults,n)}function i(){function c(b,c){return a("<"+b+' xmlns="urn:schemas-microsoft.com:vml" class="spin-vml">',c)}k.addRule(".spin-vml","behavior:url(#default#VML)"),h.prototype.lines=function(a,d){function f(){return e(c("group",{coordsize:k+" "+k,coordorigin:-j+" "+-j}),{width:k,height:k})}function h(a,h,i){b(m,b(e(f(),{rotation:360/d.lines*a+"deg",left:~~h}),b(e(c("roundrect",{arcsize:d.corners}),{width:j,height:d.scale*d.width,left:d.scale*d.radius,top:-d.scale*d.width>>1,filter:i}),c("fill",{color:g(d.color,a),opacity:d.opacity}),c("stroke",{opacity:0}))))}var i,j=d.scale*(d.length+d.width),k=2*d.scale*j,l=-(d.width+d.length)*d.scale*2+"px",m=e(f(),{position:"absolute",top:l,left:l});if(d.shadow)for(i=1;i<=d.lines;i++)h(i,-2,"progid:DXImageTransform.Microsoft.Blur(pixelradius=2,makeshadow=1,shadowopacity=.3)");for(i=1;i<=d.lines;i++)h(i);return b(a,m)},h.prototype.opacity=function(a,b,c,d){var e=a.firstChild;d=d.shadow&&d.lines||0,e&&b+d<e.childNodes.length&&(e=e.childNodes[b+d],e=e&&e.firstChild,e=e&&e.firstChild,e&&(e.opacity=c))}}var j,k,l=["webkit","Moz","ms","O"],m={},n={lines:12,length:7,width:5,radius:10,scale:1,corners:1,color:"#000",opacity:.25,rotate:0,direction:1,speed:1,trail:100,fps:20,zIndex:2e9,className:"spinner",top:"50%",left:"50%",shadow:!1,hwaccel:!1,position:"absolute"};if(h.defaults={},f(h.prototype,{spin:function(b){this.stop();var c=this,d=c.opts,f=c.el=a(null,{className:d.className});if(e(f,{position:d.position,width:0,zIndex:d.zIndex,left:d.left,top:d.top}),b&&b.insertBefore(f,b.firstChild||null),f.setAttribute("role","progressbar"),c.lines(f,c.opts),!j){var g,h=0,i=(d.lines-1)*(1-d.direction)/2,k=d.fps,l=k/d.speed,m=(1-d.opacity)/(l*d.trail/100),n=l/d.lines;!function o(){h++;for(var a=0;a<d.lines;a++)g=Math.max(1-(h+(d.lines-a)*n)%l*m,d.opacity),c.opacity(f,a*d.direction+i,g,d);c.timeout=c.el&&setTimeout(o,~~(1e3/k))}()}return c},stop:function(){var a=this.el;return a&&(clearTimeout(this.timeout),a.parentNode&&a.parentNode.removeChild(a),this.el=void 0),this},lines:function(d,f){function h(b,c){return e(a(),{position:"absolute",width:f.scale*(f.length+f.width)+"px",height:f.scale*f.width+"px",background:b,boxShadow:c,transformOrigin:"left",transform:"rotate("+~~(360/f.lines*k+f.rotate)+"deg) translate("+f.scale*f.radius+"px,0)",borderRadius:(f.corners*f.scale*f.width>>1)+"px"})}for(var i,k=0,l=(f.lines-1)*(1-f.direction)/2;k<f.lines;k++)i=e(a(),{position:"absolute",top:1+~(f.scale*f.width/2)+"px",transform:f.hwaccel?"translate3d(0,0,0)":"",opacity:f.opacity,animation:j&&c(f.opacity,f.trail,l+k*f.direction,f.lines)+" "+1/f.speed+"s linear infinite"}),f.shadow&&b(i,e(h("#000","0 0 4px #000"),{top:"2px"})),b(d,b(i,h(g(f.color,k),"0 0 1px rgba(0,0,0,.1)")));return d},opacity:function(a,b,c){b<a.childNodes.length&&(a.childNodes[b].style.opacity=c)}}),"undefined"!=typeof document){k=function(){var c=a("style",{type:"text/css"});return b(document.getElementsByTagName("head")[0],c),c.sheet||c.styleSheet}();var o=e(a("group"),{behavior:"url(#default#VML)"});!d(o,"transform")&&o.adj?i():j=d(o,"animation")}return h});!function(e){var r={};function n(t){if(r[t])return r[t].exports;var i=r[t]={i:t,l:!1,exports:{}};return e[t].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=e,n.c=r,n.d=function(e,r,t){n.o(e,r)||Object.defineProperty(e,r,{enumerable:!0,get:t})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,r){if(1&r&&(e=n(e)),8&r)return e;if(4&r&&"object"==typeof e&&e&&e.__esModule)return e;var t=Object.create(null);if(n.r(t),Object.defineProperty(t,"default",{enumerable:!0,value:e}),2&r&&"string"!=typeof e)for(var i in e)n.d(t,i,function(r){return e[r]}.bind(null,i));return t},n.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(r,"a",r),r},n.o=function(e,r){return Object.prototype.hasOwnProperty.call(e,r)},n.p="",n(n.s=0)}([function(e,r){function n(e,r){pluginHandle.consentDialog(!1),e?callbacks.error(e):streamsDone(handleId,jsep,media,callbacks,r)}function t(e,r,n){o.log("Adding media constraint (screen capture)"),o.debug(e),navigator.mediaDevices.getUserMedia(e).then(function(e){n?navigator.mediaDevices.getUserMedia({audio:!0,video:!1}).then(function(n){e.addTrack(n.getAudioTracks()[0]),r(null,e)}):r(null,e)}).catch(function(e){pluginHandle.consentDialog(!1),r(e)})}o.sessions={},o.isExtensionEnabled=function(){if(navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)return!0;if(window.navigator.userAgent.match("Chrome")){var e=parseInt(window.navigator.userAgent.match(/Chrome\/(.*) /)[1],10),r=33;return window.navigator.userAgent.match("Linux")&&(r=35),e>=26&&e<=r||o.extension.isInstalled()}return!0};var i={extensionId:"hapfgfdkleiggjjpfpenajgdnfckjpaj",isInstalled:function(){return null!==document.querySelector("#janus-extension-installed")},getScreen:function(e){var r=window.setTimeout(function(){let r=new Error("NavigatorUserMediaError");return r.name='The required Chrome extension is not installed: click <a href="#">here</a> to install it. (NOTE: this will need you to refresh the page)',e(r)},1e3);this.cache[r]=e,window.postMessage({type:"janusGetScreen",id:r},"*")},init:function(){var e={};this.cache=e,window.addEventListener("message",function(r){if(r.origin==window.location.origin)if("janusGotScreen"==r.data.type&&e[r.data.id]){var n=e[r.data.id];if(delete e[r.data.id],""===r.data.sourceId){var t=new Error("NavigatorUserMediaError");t.name="You cancelled the request for permission, giving up...",n(t)}else n(null,r.data.sourceId)}else"janusGetScreenPending"==r.data.type&&(console.log("clearing ",r.data.id),window.clearTimeout(r.data.id))})}};function o(e){if(void 0===o.initDone)return e.error("Library not initialized"),{};if(!o.isWebrtcSupported())return e.error("WebRTC not supported by this browser"),{};if(o.log("Library initialized: "+o.initDone),(e=e||{}).success="function"==typeof e.success?e.success:o.noop,e.error="function"==typeof e.error?e.error:o.noop,e.destroyed="function"==typeof e.destroyed?e.destroyed:o.noop,null===e.server||void 0===e.server)return e.error("Invalid server url"),{};var r=!1,i=null,a={},d=null,s=null,l=0,c=e.server;o.isArray(c)?(o.log("Multiple servers provided ("+c.length+"), will use the first that works"),c=null,s=e.server,o.debug(s)):0===c.indexOf("ws")?(r=!0,o.log("Using WebSockets to contact Janus: "+c)):(r=!1,o.log("Using REST API to contact Janus: "+c));var u=e.iceServers;null==u&&(u=[{urls:"stun:stun.l.google.com:19302"}]);var v=e.iceTransportPolicy,f=e.bundlePolicy,p=e.ipv6;null==p&&(p=!1);var g=!1;void 0!==e.withCredentials&&null!==e.withCredentials&&(g=!0===e.withCredentials);var m=10;void 0!==e.max_poll_events&&null!==e.max_poll_events&&(m=e.max_poll_events),m<1&&(m=1);var b=null;void 0!==e.token&&null!==e.token&&(b=e.token);var h=null;void 0!==e.apisecret&&null!==e.apisecret&&(h=e.apisecret),this.destroyOnUnload=!0,void 0!==e.destroyOnUnload&&null!==e.destroyOnUnload&&(this.destroyOnUnload=!0===e.destroyOnUnload);var w=25e3;void 0!==e.keepAlivePeriod&&null!==e.keepAlivePeriod&&(w=e.keepAlivePeriod),isNaN(w)&&(w=25e3);var y=6e4;void 0!==e.longPollTimeout&&null!==e.longPollTimeout&&(y=e.longPollTimeout),isNaN(y)&&(y=6e4);var S=!1,k=null,T={},C=this,D=0,A={};function R(){if(null!=k)if(o.debug("Long poll..."),S){var r=c+"/"+k+"?rid="+(new Date).getTime();null!=m&&(r=r+"&maxev="+m),null!=b&&(r=r+"&token="+encodeURIComponent(b)),null!=h&&(r=r+"&apisecret="+encodeURIComponent(h)),o.httpAPICall(r,{verb:"GET",withCredentials:g,success:I,timeout:y,error:function(r,n){if(o.error(r+":",n),++D>3)return S=!1,void e.error("Lost connection to the server (is it down?)");R()}})}else o.warn("Is the server down? (connected=false)")}function I(e,n){if(D=0,r||null==k||!0===n||R(),r||!o.isArray(e))if("keepalive"!==e.janus)if("ack"!==e.janus)if("success"!==e.janus)if("trickle"===e.janus){if(null==(l=e.sender))return void o.warn("Missing sender...");if(null==(u=T[l]))return void o.debug("This handle is not attached to this session");var t=e.candidate;o.debug("Got a trickled candidate on session "+k),o.debug(t);var a=u.webrtcStuff;a.pc&&a.remoteSdp?(o.debug("Adding remote candidate:",t),t&&!0!==t.completed?a.pc.addIceCandidate(t):a.pc.addIceCandidate()):(o.debug("We didn't do setRemoteDescription (trickle got here before the offer?), caching candidate"),a.candidates||(a.candidates=[]),a.candidates.push(t),o.debug(a.candidates))}else{if("webrtcup"===e.janus)return o.debug("Got a webrtcup event on session "+k),o.debug(e),null==(l=e.sender)?void o.warn("Missing sender..."):null==(u=T[l])?void o.debug("This handle is not attached to this session"):void u.webrtcState(!0);if("hangup"===e.janus){if(o.debug("Got a hangup event on session "+k),o.debug(e),null==(l=e.sender))return void o.warn("Missing sender...");if(null==(u=T[l]))return void o.debug("This handle is not attached to this session");u.webrtcState(!1,e.reason),u.hangup()}else if("detached"===e.janus){if(o.debug("Got a detached event on session "+k),o.debug(e),null==(l=e.sender))return void o.warn("Missing sender...");if(null==(u=T[l]))return;u.detached=!0,u.ondetached(),u.detach()}else if("media"===e.janus){if(o.debug("Got a media event on session "+k),o.debug(e),null==(l=e.sender))return void o.warn("Missing sender...");if(null==(u=T[l]))return void o.debug("This handle is not attached to this session");u.mediaState(e.type,e.receiving)}else if("slowlink"===e.janus){if(o.debug("Got a slowlink event on session "+k),o.debug(e),null==(l=e.sender))return void o.warn("Missing sender...");if(null==(u=T[l]))return void o.debug("This handle is not attached to this session");u.slowLink(e.uplink,e.nacks)}else{if("error"===e.janus){var d,s;if(o.error("Ooops: "+e.error.code+" "+e.error.reason),o.debug(e),null!=(d=e.transaction))null!=(s=A[d])&&s(e),delete A[d];return}if("event"===e.janus){var l;if(o.debug("Got a plugin event on session "+k),o.debug(e),null==(l=e.sender))return void o.warn("Missing sender...");var c=e.plugindata;if(null==c)return void o.warn("Missing plugindata...");o.debug("  -- Event is coming from "+l+" ("+c.plugin+")");var u,v=c.data;if(o.debug(v),null==(u=T[l]))return void o.warn("This handle is not attached to this session");var f=e.jsep;null!=f&&(o.debug("Handling SDP as well..."),o.debug(f));var p=u.onmessage;null!=p?(o.debug("Notifying application..."),p(v,f)):o.debug("No provided notification callback")}else{if("timeout"===e.janus)return o.error("Timeout on session "+k),o.debug(e),void(r&&i.close(3504,"Gateway timeout"));o.warn("Unknown message/event  '"+e.janus+"' on session "+k),o.debug(e)}}}else o.debug("Got a success on session "+k),o.debug(e),null!=(d=e.transaction)&&(null!=(s=A[d])&&s(e),delete A[d]);else o.debug("Got an ack on session "+k),o.debug(e),null!=(d=e.transaction)&&(null!=(s=A[d])&&s(e),delete A[d]);else o.vdebug("Got a keepalive on session "+k);else for(var g=0;g<e.length;g++)I(e[g],!0)}function x(){if(null!==c&&r&&S){d=setTimeout(x,w);var e={janus:"keepalive",session_id:k,transaction:o.randomString(12)};null!=b&&(e.token=b),null!=h&&(e.apisecret=h),i.send(JSON.stringify(e))}}function V(n){var t=o.randomString(12),u={janus:"create",transaction:t};if(n.reconnect&&(S=!1,u.janus="claim",u.session_id=k,i&&(i.onopen=null,i.onerror=null,i.onclose=null,d&&(clearTimeout(d),d=null))),null!=b&&(u.token=b),null!=h&&(u.apisecret=h),null===c&&o.isArray(s)&&(0===(c=s[l]).indexOf("ws")?(r=!0,o.log("Server #"+(l+1)+": trying WebSockets to contact Janus ("+c+")")):(r=!1,o.log("Server #"+(l+1)+": trying REST API to contact Janus ("+c+")"))),r)for(var v in i=o.newWebSocket(c,"janus-protocol"),a={error:function(){if(o.error("Error connecting to the Janus WebSockets server... "+c),o.isArray(s)&&!n.reconnect)return++l==s.length?void n.error("Error connecting to any of the provided Janus servers: Is the server down?"):(c=null,void setTimeout(function(){V(n)},200));n.error("Error connecting to the Janus WebSockets server: Is the server down?")},open:function(){A[t]=function(e){if(o.debug(e),"success"!==e.janus)return o.error("Ooops: "+e.error.code+" "+e.error.reason),void n.error(e.error.reason);d=setTimeout(x,w),S=!0,k=e.session_id?e.session_id:e.data.id,n.reconnect?o.log("Claimed session: "+k):o.log("Created session: "+k),o.sessions[k]=C,n.success()},i.send(JSON.stringify(u))},message:function(e){I(JSON.parse(e.data))},close:function(){null!==c&&S&&(S=!1,e.error("Lost connection to the server (is it down?)"))}})i.addEventListener(v,a[v]);else o.httpAPICall(c,{verb:"POST",withCredentials:g,body:u,success:function(e){if(o.debug(e),"success"!==e.janus)return o.error("Ooops: "+e.error.code+" "+e.error.reason),void n.error(e.error.reason);S=!0,k=e.session_id?e.session_id:e.data.id,n.reconnect?o.log("Claimed session: "+k):o.log("Created session: "+k),o.sessions[k]=C,R(),n.success()},error:function(e,r){if(o.error(e+":",r),o.isArray(s)&&!n.reconnect)return++l==s.length?void n.error("Error connecting to any of the provided Janus servers: Is the server down?"):(c=null,void setTimeout(function(){V(n)},200));""===r?n.error(e+": Is the server down?"):n.error(e+": "+r)}})}function j(e,n){if((n=n||{}).success="function"==typeof n.success?n.success:o.noop,n.error="function"==typeof n.error?n.error:o.noop,!S)return o.warn("Is the server down? (connected=false)"),void n.error("Is the server down? (connected=false)");var t=T[e];if(null==t||null===t.webrtcStuff||void 0===t.webrtcStuff)return o.warn("Invalid handle"),void n.error("Invalid handle");var a=n.message,d=n.jsep,s=o.randomString(12),l={janus:"message",body:a,transaction:s};if(null!==t.token&&void 0!==t.token&&(l.token=t.token),null!=h&&(l.apisecret=h),null!=d&&(l.jsep=d),o.debug("Sending message to plugin (handle="+e+"):"),o.debug(l),r)return l.session_id=k,l.handle_id=e,A[s]=function(e){if(o.debug("Message sent!"),o.debug(e),"success"===e.janus){var r=e.plugindata;if(null==r)return o.warn("Request succeeded, but missing plugindata..."),void n.success();o.log("Synchronous transaction successful ("+r.plugin+")");var t=r.data;return o.debug(t),void n.success(t)}"ack"===e.janus?n.success():void 0!==e.error&&null!==e.error?(o.error("Ooops: "+e.error.code+" "+e.error.reason),n.error(e.error.code+" "+e.error.reason)):(o.error("Unknown error"),n.error("Unknown error"))},void i.send(JSON.stringify(l));o.httpAPICall(c+"/"+k+"/"+e,{verb:"POST",withCredentials:g,body:l,success:function(e){if(o.debug("Message sent!"),o.debug(e),"success"===e.janus){var r=e.plugindata;if(null==r)return o.warn("Request succeeded, but missing plugindata..."),void n.success();o.log("Synchronous transaction successful ("+r.plugin+")");var t=r.data;return o.debug(t),void n.success(t)}"ack"===e.janus?n.success():void 0!==e.error&&null!==e.error?(o.error("Ooops: "+e.error.code+" "+e.error.reason),n.error(e.error.code+" "+e.error.reason)):(o.error("Unknown error"),n.error("Unknown error"))},error:function(e,r){o.error(e+":",r),n.error(e+": "+r)}})}function P(e,n){if(S){var t=T[e];if(null!=t&&null!==t.webrtcStuff&&void 0!==t.webrtcStuff){var a={janus:"trickle",candidate:n,transaction:o.randomString(12)};if(null!==t.token&&void 0!==t.token&&(a.token=t.token),null!=h&&(a.apisecret=h),o.vdebug("Sending trickle candidate (handle="+e+"):"),o.vdebug(a),r)return a.session_id=k,a.handle_id=e,void i.send(JSON.stringify(a));o.httpAPICall(c+"/"+k+"/"+e,{verb:"POST",withCredentials:g,body:a,success:function(e){o.vdebug("Candidate sent!"),o.vdebug(e),"ack"===e.janus||o.error("Ooops: "+e.error.code+" "+e.error.reason)},error:function(e,r){o.error(e+":",r)}})}else o.warn("Invalid handle")}else o.warn("Is the server down? (connected=false)")}function M(e,r,n,t){var i=T[e];if(null!=i&&null!==i.webrtcStuff&&void 0!==i.webrtcStuff){var a=i.webrtcStuff,d=function(e){o.log("Received state change on data channel:",e);var r=e.target.label,n=a.dataChannel[r]?a.dataChannel[r].readyState:"null";if(o.log("State change on <"+r+"> data channel: "+n),"open"===n){if(a.dataChannel[r].pending&&a.dataChannel[r].pending.length>0){for(var t in o.log("Sending pending messages on <' + label + '>:",a.dataChannel[r].pending.length),a.dataChannel[r].pending){var d=a.dataChannel[r].pending[t];o.log("Sending string on data channel <"+r+">: "+d),a.dataChannel[r].send(d)}a.dataChannel[r].pending=[]}i.ondataopen(r)}};a.dataChannel[r]=n||a.pc.createDataChannel(r,{ordered:!1}),a.dataChannel[r].onmessage=function(e){o.log("Received message on data channel:",e);var r=e.target.label;i.ondata(e.data,r)},a.dataChannel[r].onopen=d,a.dataChannel[r].onclose=d,a.dataChannel[r].onerror=function(e){o.error("Got error on data channel:",e)},a.dataChannel[r].pending=[],t&&a.dataChannel[r].pending.push(t)}else o.warn("Invalid handle")}function O(e,r){(r=r||{}).success="function"==typeof r.success?r.success:o.noop,r.error="function"==typeof r.error?r.error:o.noop;var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return o.warn("Invalid handle"),void r.error("Invalid handle");var t=n.webrtcStuff,i=r.text;if(null==i)return o.warn("Invalid text"),void r.error("Invalid text");var a=r.label?r.label:o.dataChanDefaultLabel;return t.dataChannel[a]?"open"!==t.dataChannel[a].readyState?(t.dataChannel[a].pending.push(i),void r.success()):(o.log("Sending string on data channel <"+a+">: "+i),t.dataChannel[a].send(i),void r.success()):(M(e,a,!1,i),void r.success())}function B(e,r){(r=r||{}).success="function"==typeof r.success?r.success:o.noop,r.error="function"==typeof r.error?r.error:o.noop;var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return o.warn("Invalid handle"),void r.error("Invalid handle");var t=n.webrtcStuff;if(null===t.dtmfSender||void 0===t.dtmfSender){if(void 0!==t.pc&&null!==t.pc){var i=t.pc.getSenders().find(function(e){return e.track&&"audio"===e.track.kind});if(!i)return o.warn("Invalid DTMF configuration (no audio track)"),void r.error("Invalid DTMF configuration (no audio track)");t.dtmfSender=i.dtmf,t.dtmfSender&&(o.log("Created DTMF Sender"),t.dtmfSender.ontonechange=function(e){o.debug("Sent DTMF tone: "+e.tone)})}if(null===t.dtmfSender||void 0===t.dtmfSender)return o.warn("Invalid DTMF configuration"),void r.error("Invalid DTMF configuration")}var a=r.dtmf;if(null==a)return o.warn("Invalid DTMF parameters"),void r.error("Invalid DTMF parameters");var d=a.tones;if(null==d)return o.warn("Invalid DTMF string"),void r.error("Invalid DTMF string");var s=a.duration;null==s&&(s=500);var l=a.gap;null==l&&(l=50),o.debug("Sending DTMF string "+d+" (duration "+s+"ms, gap "+l+"ms)"),t.dtmfSender.insertDTMF(d,s,l),r.success()}function E(e,n){(n=n||{}).success="function"==typeof n.success?n.success:o.noop,n.error="function"==typeof n.error?n.error:o.noop;var t=!0;void 0!==n.asyncRequest&&null!==n.asyncRequest&&(t=!0===n.asyncRequest);var a=!1;void 0!==n.noRequest&&null!==n.noRequest&&(a=!0===n.noRequest),o.log("Destroying handle "+e+" (async="+t+")"),Y(e);var d=T[e];if(null==d||d.detached)return delete T[e],void n.success();if(a)return delete T[e],void n.success();if(!S)return o.warn("Is the server down? (connected=false)"),void n.error("Is the server down? (connected=false)");var s={janus:"detach",transaction:o.randomString(12)};if(null!==d.token&&void 0!==d.token&&(s.token=d.token),null!=h&&(s.apisecret=h),r)return s.session_id=k,s.handle_id=e,i.send(JSON.stringify(s)),delete T[e],void n.success();o.httpAPICall(c+"/"+k+"/"+e,{verb:"POST",async:t,withCredentials:g,body:s,success:function(r){o.log("Destroyed handle:"),o.debug(r),"success"!==r.janus&&o.error("Ooops: "+r.error.code+" "+r.error.reason),delete T[e],n.success()},error:function(r,t){o.error(r+":",t),delete T[e],n.success()}})}function _(e,r,n,t,i){var a=!1;null!==t.media.update&&!0===t.media.update&&(a=!0);var d=T[e];if(null==d||null===d.webrtcStuff||void 0===d.webrtcStuff)return o.warn("Invalid handle"),void t.error("Invalid handle");var s=d.webrtcStuff;o.debug("streamsDone:",i),i&&(o.debug("  -- Audio tracks:",i.getAudioTracks()),o.debug("  -- Video tracks:",i.getVideoTracks()));var l=!1;if(s.myStream&&n.update&&!s.streamExternal){if((!n.update&&K(n)||n.update&&(n.addAudio||n.replaceAudio))&&i.getAudioTracks()&&i.getAudioTracks().length)if(s.myStream.addTrack(i.getAudioTracks()[0]),"firefox"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=59||"safari"===o.webRTCAdapter.browserDetails.browser&&window.RTCRtpSender.prototype.replaceTrack||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72){o.log((n.replaceAudio?"Replacing":"Adding")+" audio track:",i.getAudioTracks()[0]);var c=null;if((m=s.pc.getTransceivers())&&m.length>0)for(var g in m){if((h=m[g]).sender&&h.sender.track&&"audio"===h.sender.track.kind||h.receiver&&h.receiver.track&&"audio"===h.receiver.track.kind){c=h;break}}c&&c.sender?c.sender.replaceTrack(i.getAudioTracks()[0]):s.pc.addTrack(i.getAudioTracks()[0],i)}else o.log((n.replaceAudio?"Replacing":"Adding")+" audio track:",i.getAudioTracks()[0]),s.pc.addTrack(i.getAudioTracks()[0],i);if((!n.update&&Z(n)||n.update&&(n.addVideo||n.replaceVideo))&&i.getVideoTracks()&&i.getVideoTracks().length)if(s.myStream.addTrack(i.getVideoTracks()[0]),"firefox"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=59||"safari"===o.webRTCAdapter.browserDetails.browser&&window.RTCRtpSender.prototype.replaceTrack||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72){o.log((n.replaceVideo?"Replacing":"Adding")+" video track:",i.getVideoTracks()[0]);var m,b=null;if((m=s.pc.getTransceivers())&&m.length>0)for(var g in m){var h;if((h=m[g]).sender&&h.sender.track&&"video"===h.sender.track.kind||h.receiver&&h.receiver.track&&"video"===h.receiver.track.kind){b=h;break}}b&&b.sender?b.sender.replaceTrack(i.getVideoTracks()[0]):s.pc.addTrack(i.getVideoTracks()[0],i)}else o.log((n.replaceVideo?"Replacing":"Adding")+" video track:",i.getVideoTracks()[0]),s.pc.addTrack(i.getVideoTracks()[0],i)}else s.myStream=i,l=!0;if(!s.pc){var w={iceServers:u,iceTransportPolicy:v,bundlePolicy:f};"chrome"===o.webRTCAdapter.browserDetails.browser&&(w.sdpSemantics=o.webRTCAdapter.browserDetails.version<72?"plan-b":"unified-plan");var y={optional:[{DtlsSrtpKeyAgreement:!0}]};if(!0===p&&y.optional.push({googIPv6:!0}),t.rtcConstraints&&"object"==typeof t.rtcConstraints)for(var g in o.debug("Adding custom PeerConnection constraints:",t.rtcConstraints),t.rtcConstraints)y.optional.push(t.rtcConstraints[g]);"edge"===o.webRTCAdapter.browserDetails.browser&&(w.bundlePolicy="max-bundle"),o.log("Creating PeerConnection"),o.debug(y),s.pc=new RTCPeerConnection(w,y),o.debug(s.pc),s.pc.getStats&&(s.volume={},s.bitrate.value="0 kbits/sec"),o.log("Preparing local SDP and gathering candidates (trickle="+s.trickle+")"),s.pc.oniceconnectionstatechange=function(e){s.pc&&d.iceState(s.pc.iceConnectionState)},s.pc.onicecandidate=function(r){if(null==r.candidate||"edge"===o.webRTCAdapter.browserDetails.browser&&r.candidate.candidate.indexOf("endOfCandidates")>0)o.log("End of candidates."),s.iceDone=!0,!0===s.trickle?P(e,{completed:!0}):function(e,r){(r=r||{}).success="function"==typeof r.success?r.success:o.noop,r.error="function"==typeof r.error?r.error:o.noop;var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return void o.warn("Invalid handle, not sending anything");var t=n.webrtcStuff;if(o.log("Sending offer/answer SDP..."),null===t.mySdp||void 0===t.mySdp)return void o.warn("Local SDP instance is invalid, not sending anything...");t.mySdp={type:t.pc.localDescription.type,sdp:t.pc.localDescription.sdp},!1===t.trickle&&(t.mySdp.trickle=!1);o.debug(r),t.sdpSent=!0,r.success(t.mySdp)}(e,t);else{var n={candidate:r.candidate.candidate,sdpMid:r.candidate.sdpMid,sdpMLineIndex:r.candidate.sdpMLineIndex};!0===s.trickle&&P(e,n)}},s.pc.ontrack=function(e){o.log("Handling Remote Track"),o.debug(e),e.streams&&(s.remoteStream=e.streams[0],d.onremotestream(s.remoteStream),e.track&&!e.track.onended&&(o.log("Adding onended callback to track:",e.track),e.track.onended=function(e){o.log("Remote track removed:",e),s.remoteStream&&(s.remoteStream.removeTrack(e.target),d.onremotestream(s.remoteStream))}))}}if(l&&null!=i){o.log("Adding local stream");var S=!0===t.simulcast2;i.getTracks().forEach(function(e){o.log("Adding local track:",e),S?"audio"===e.kind?s.pc.addTrack(e,i):(o.log("Enabling rid-based simulcasting:",e),s.pc.addTransceiver(e,{direction:"sendrecv",streams:[i],sendEncodings:[{rid:"h",active:!0,maxBitrate:9e5},{rid:"m",active:!0,maxBitrate:3e5,scaleResolutionDownBy:2},{rid:"l",active:!0,maxBitrate:1e5,scaleResolutionDownBy:4}]})):s.pc.addTrack(e,i)})}if(function(e){if(o.debug("isDataEnabled:",e),"edge"==o.webRTCAdapter.browserDetails.browser)return o.warn("Edge doesn't support data channels yet"),!1;return null!=e&&!0===e.data}(n)&&!s.dataChannel[o.dataChanDefaultLabel]&&(o.log("Creating data channel"),M(e,o.dataChanDefaultLabel,!1),s.pc.ondatachannel=function(r){o.log("Data channel created by Janus:",r),M(e,r.channel.label,r.channel)}),s.myStream){let e=!0;a&&(e=!1),d.onlocalstream(s.myStream,e)}null==r?function(e,r,n){(n=n||{}).success="function"==typeof n.success?n.success:o.noop,n.error="function"==typeof n.error?n.error:o.noop,n.customizeSdp="function"==typeof n.customizeSdp?n.customizeSdp:o.noop;var t=T[e];if(null==t||null===t.webrtcStuff||void 0===t.webrtcStuff)return o.warn("Invalid handle"),void n.error("Invalid handle");var i=t.webrtcStuff,a=!0===n.simulcast;a?o.log("Creating offer (iceDone="+i.iceDone+", simulcast="+a+")"):o.log("Creating offer (iceDone="+i.iceDone+")");var d={};if("firefox"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=59||"safari"===o.webRTCAdapter.browserDetails.browser&&window.RTCRtpSender.prototype.replaceTrack||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72){var s=null,l=null,c=i.pc.getTransceivers();if(c&&c.length>0)for(var u in c){var v=c[u];v.sender&&v.sender.track&&"audio"===v.sender.track.kind||v.receiver&&v.receiver.track&&"audio"===v.receiver.track.kind?s||(s=v):(v.sender&&v.sender.track&&"video"===v.sender.track.kind||v.receiver&&v.receiver.track&&"video"===v.receiver.track.kind)&&(l||(l=v))}var f=K(r),p=Q(r);f||p?f&&p?s&&(s.setDirection?s.setDirection("sendrecv"):s.direction="sendrecv",o.log("Setting audio transceiver to sendrecv:",s)):f&&!p?s&&(s.setDirection?s.setDirection("sendonly"):s.direction="sendonly",o.log("Setting audio transceiver to sendonly:",s)):!f&&p&&(s?(s.setDirection?s.setDirection("recvonly"):s.direction="recvonly",o.log("Setting audio transceiver to recvonly:",s)):(s=i.pc.addTransceiver("audio",{direction:"recvonly"}),o.log("Adding recvonly audio transceiver:",s))):r.removeAudio&&s&&(s.setDirection?s.setDirection("inactive"):s.direction="inactive",o.log("Setting audio transceiver to inactive:",s));var g=Z(r),m=$(r);g||m?g&&m?l&&(l.setDirection?l.setDirection("sendrecv"):l.direction="sendrecv",o.log("Setting video transceiver to sendrecv:",l)):g&&!m?l&&(l.setDirection?l.setDirection("sendonly"):l.direction="sendonly",o.log("Setting video transceiver to sendonly:",l)):!g&&m&&(l?(l.setDirection?l.setDirection("recvonly"):l.direction="recvonly",o.log("Setting video transceiver to recvonly:",l)):(l=i.pc.addTransceiver("video",{direction:"recvonly"}),o.log("Adding recvonly video transceiver:",l))):r.removeVideo&&l&&(l.setDirection?l.setDirection("inactive"):l.direction="inactive",o.log("Setting video transceiver to inactive:",l))}else d.offerToReceiveAudio=Q(r),d.offerToReceiveVideo=$(r);!0===n.iceRestart&&(d.iceRestart=!0);o.debug(d);var b=Z(r);if(b&&a&&"firefox"===o.webRTCAdapter.browserDetails.browser){o.log("Enabling Simulcasting for Firefox (RID)");var h=i.pc.getSenders().find(function(e){return"video"==e.track.kind});if(h){var w=h.getParameters();w||(w={}),w.encodings=[{rid:"h",active:!0,maxBitrate:9e5},{rid:"m",active:!0,maxBitrate:3e5,scaleResolutionDownBy:2},{rid:"l",active:!0,maxBitrate:1e5,scaleResolutionDownBy:4}],h.setParameters(w)}}i.pc.createOffer(d).then(function(e){o.debug(e);var r={type:e.type,sdp:e.sdp};n.customizeSdp(r),e.sdp=r.sdp,o.log("Setting local description"),b&&a&&("chrome"===o.webRTCAdapter.browserDetails.browser||"safari"===o.webRTCAdapter.browserDetails.browser?(o.log("Enabling Simulcasting for Chrome (SDP munging)"),e.sdp=function(e){for(var r=e.split("\r\n"),n=!1,t=[-1],i=[-1],a=null,d=null,s=null,l=null,c=-1,u=0;u<r.length;u++){var v=r[u].match(/m=(\w+) */);if(v){var f=v[1];if("video"===f){if(!(t[0]<0)){c=u;break}n=!0}else if(t[0]>-1){c=u;break}}else if(n){var p=r[u].match(/a=ssrc-group:FID (\d+) (\d+)/);if(p)t[0]=p[1],i[0]=p[2],r.splice(u,1),u--;else{if(t[0]){var g=r[u].match("a=ssrc:"+t[0]+" cname:(.+)");if(g&&(a=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" msid:(.+)"))&&(d=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" mslabel:(.+)"))&&(s=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" label:(.+)"))&&(l=g[1]),0===r[u].indexOf("a=ssrc:"+i[0])){r.splice(u,1),u--;continue}if(0===r[u].indexOf("a=ssrc:"+t[0])){r.splice(u,1),u--;continue}}0!=r[u].length||(r.splice(u,1),u--)}}}if(t[0]<0){c=-1,n=!1;for(var u=0;u<r.length;u++){var v=r[u].match(/m=(\w+) */);if(v){var f=v[1];if("video"===f){if(!(t[0]<0)){c=u;break}n=!0}else if(t[0]>-1){c=u;break}}else if(n){if(t[0]<0){var m=r[u].match(/a=ssrc:(\d+)/);if(m){t[0]=m[1],r.splice(u,1),u--;continue}}else{var g=r[u].match("a=ssrc:"+t[0]+" cname:(.+)");if(g&&(a=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" msid:(.+)"))&&(d=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" mslabel:(.+)"))&&(s=g[1]),(g=r[u].match("a=ssrc:"+t[0]+" label:(.+)"))&&(l=g[1]),0===r[u].indexOf("a=ssrc:"+i[0])){r.splice(u,1),u--;continue}if(0===r[u].indexOf("a=ssrc:"+t[0])){r.splice(u,1),u--;continue}}0!=r[u].length||(r.splice(u,1),u--)}}}if(t[0]<0)return o.warn("Couldn't find the video SSRC, simulcasting NOT enabled"),e;c<0&&(c=r.length);t[1]=Math.floor(4294967295*Math.random()),t[2]=Math.floor(4294967295*Math.random()),i[1]=Math.floor(4294967295*Math.random()),i[2]=Math.floor(4294967295*Math.random());for(var u=0;u<t.length;u++)a&&(r.splice(c,0,"a=ssrc:"+t[u]+" cname:"+a),c++),d&&(r.splice(c,0,"a=ssrc:"+t[u]+" msid:"+d),c++),s&&(r.splice(c,0,"a=ssrc:"+t[u]+" mslabel:"+s),c++),l&&(r.splice(c,0,"a=ssrc:"+t[u]+" label:"+l),c++),a&&(r.splice(c,0,"a=ssrc:"+i[u]+" cname:"+a),c++),d&&(r.splice(c,0,"a=ssrc:"+i[u]+" msid:"+d),c++),s&&(r.splice(c,0,"a=ssrc:"+i[u]+" mslabel:"+s),c++),l&&(r.splice(c,0,"a=ssrc:"+i[u]+" label:"+l),c++);r.splice(c,0,"a=ssrc-group:FID "+t[2]+" "+i[2]),r.splice(c,0,"a=ssrc-group:FID "+t[1]+" "+i[1]),r.splice(c,0,"a=ssrc-group:FID "+t[0]+" "+i[0]),r.splice(c,0,"a=ssrc-group:SIM "+t[0]+" "+t[1]+" "+t[2]),(e=r.join("\r\n")).endsWith("\r\n")||(e+="\r\n");return e}(e.sdp)):"firefox"!==o.webRTCAdapter.browserDetails.browser&&o.warn("simulcast=true, but this is not Chrome nor Firefox, ignoring")),i.mySdp=e.sdp,i.pc.setLocalDescription(e).catch(n.error),i.mediaConstraints=d,i.iceDone||i.trickle?(o.log("Offer ready"),o.debug(n),n.success(e)):o.log("Waiting for all candidates...")},n.error)}(e,n,t):s.pc.setRemoteDescription(r).then(function(){if(o.log("Remote description accepted!"),s.remoteSdp=r.sdp,s.candidates&&s.candidates.length>0){for(var i in s.candidates){var a=s.candidates[i];o.debug("Adding remote candidate:",a),a&&!0!==a.completed?s.pc.addIceCandidate(a):s.pc.addIceCandidate()}s.candidates=[]}!function(e,r,n){(n=n||{}).success="function"==typeof n.success?n.success:o.noop,n.error="function"==typeof n.error?n.error:o.noop,n.customizeSdp="function"==typeof n.customizeSdp?n.customizeSdp:o.noop;var t=T[e];if(null==t||null===t.webrtcStuff||void 0===t.webrtcStuff)return o.warn("Invalid handle"),void n.error("Invalid handle");var i=t.webrtcStuff,a=!0===n.simulcast;a?o.log("Creating answer (iceDone="+i.iceDone+", simulcast="+a+")"):o.log("Creating answer (iceDone="+i.iceDone+")");var d=null;if("firefox"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=59||"safari"===o.webRTCAdapter.browserDetails.browser&&window.RTCRtpSender.prototype.replaceTrack||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72){d={};var s=null,l=null,c=i.pc.getTransceivers();if(c&&c.length>0)for(var u in c){var v=c[u];v.sender&&v.sender.track&&"audio"===v.sender.track.kind||v.receiver&&v.receiver.track&&"audio"===v.receiver.track.kind?s||(s=v):(v.sender&&v.sender.track&&"video"===v.sender.track.kind||v.receiver&&v.receiver.track&&"video"===v.receiver.track.kind)&&(l||(l=v))}var f=K(r),p=Q(r);f||p?f&&p?s&&(s.setDirection?s.setDirection("sendrecv"):s.direction="sendrecv",o.log("Setting audio transceiver to sendrecv:",s)):f&&!p?s&&(s.setDirection?s.setDirection("sendonly"):s.direction="sendonly",o.log("Setting audio transceiver to sendonly:",s)):!f&&p&&(s?(s.setDirection?s.setDirection("recvonly"):s.direction="recvonly",o.log("Setting audio transceiver to recvonly:",s)):(s=i.pc.addTransceiver("audio",{direction:"recvonly"}),o.log("Adding recvonly audio transceiver:",s))):r.removeAudio&&s&&(s.setDirection?s.setDirection("inactive"):s.direction="inactive",o.log("Setting audio transceiver to inactive:",s));var g=Z(r),m=$(r);g||m?g&&m?l&&(l.setDirection?l.setDirection("sendrecv"):l.direction="sendrecv",o.log("Setting video transceiver to sendrecv:",l)):g&&!m?l&&(l.setDirection?l.setDirection("sendonly"):l.direction="sendonly",o.log("Setting video transceiver to sendonly:",l)):!g&&m&&(l?(l.setDirection?l.setDirection("recvonly"):l.direction="recvonly",o.log("Setting video transceiver to recvonly:",l)):(l=i.pc.addTransceiver("video",{direction:"recvonly"}),o.log("Adding recvonly video transceiver:",l))):r.removeVideo&&l&&(l.setDirection?l.setDirection("inactive"):l.direction="inactive",o.log("Setting video transceiver to inactive:",l))}else d="firefox"==o.webRTCAdapter.browserDetails.browser||"edge"==o.webRTCAdapter.browserDetails.browser?{offerToReceiveAudio:Q(r),offerToReceiveVideo:$(r)}:{mandatory:{OfferToReceiveAudio:Q(r),OfferToReceiveVideo:$(r)}};o.debug(d);var b=Z(r);if(b&&a&&"firefox"===o.webRTCAdapter.browserDetails.browser){o.log("Enabling Simulcasting for Firefox (RID)");var h=i.pc.getSenders()[1];o.log(h);var w=h.getParameters();o.log(w),h.setParameters({encodings:[{rid:"high",active:!0,priority:"high",maxBitrate:1e6},{rid:"medium",active:!0,priority:"medium",maxBitrate:3e5},{rid:"low",active:!0,priority:"low",maxBitrate:1e5}]})}i.pc.createAnswer(d).then(function(e){o.debug(e);var r={type:e.type,sdp:e.sdp};n.customizeSdp(r),e.sdp=r.sdp,o.log("Setting local description"),b&&a&&("chrome"===o.webRTCAdapter.browserDetails.browser?o.warn("simulcast=true, but this is an answer, and video breaks in Chrome if we enable it"):"firefox"!==o.webRTCAdapter.browserDetails.browser&&o.warn("simulcast=true, but this is not Chrome nor Firefox, ignoring")),i.mySdp=e.sdp,i.pc.setLocalDescription(e).catch(n.error),i.mediaConstraints=d,i.iceDone||i.trickle?n.success(e):o.log("Waiting for all candidates...")},n.error)}(e,n,t)},t.error)}function L(e,r,i){(i=i||{}).success="function"==typeof i.success?i.success:o.noop,i.error="function"==typeof i.error?i.error:X;var a=i.jsep;if(r&&a);else if(!(r||a&&a.type&&a.sdp))return o.error("A valid JSEP is required for createAnswer"),void i.error("A valid JSEP is required for createAnswer");i.media=i.media||{audio:!0,video:!0};var d=i.media,s=T[e];if(null==s||null===s.webrtcStuff||void 0===s.webrtcStuff)return o.warn("Invalid handle"),void i.error("Invalid handle");var l,c=s.webrtcStuff;if(c.trickle=(l=i.trickle,o.debug("isTrickleEnabled:",l),null==l||!0===l),void 0===c.pc||null===c.pc)d.update=!1,d.keepAudio=!1,d.keepVideo=!1;else if(void 0!==c.pc&&null!==c.pc){if(o.log("Updating existing media session"),d.update=!0,null!==i.stream&&void 0!==i.stream)i.stream!==c.myStream&&o.log("Renegotiation involves a new external stream");else{if(d.addAudio){if(d.keepAudio=!1,d.replaceAudio=!1,d.removeAudio=!1,d.audioSend=!0,c.myStream&&c.myStream.getAudioTracks()&&c.myStream.getAudioTracks().length)return o.error("Can't add audio stream, there already is one"),void i.error("Can't add audio stream, there already is one")}else d.removeAudio?(d.keepAudio=!1,d.replaceAudio=!1,d.addAudio=!1,d.audioSend=!1):d.replaceAudio&&(d.keepAudio=!1,d.addAudio=!1,d.removeAudio=!1,d.audioSend=!0);if(null===c.myStream||void 0===c.myStream?(d.replaceAudio&&(d.keepAudio=!1,d.replaceAudio=!1,d.addAudio=!0,d.audioSend=!0),K(d)&&(d.keepAudio=!1,d.addAudio=!0)):null===c.myStream.getAudioTracks()||void 0===c.myStream.getAudioTracks()||0===c.myStream.getAudioTracks().length?(d.replaceAudio&&(d.keepAudio=!1,d.replaceAudio=!1,d.addAudio=!0,d.audioSend=!0),K(d)&&(d.keepVideo=!1,d.addAudio=!0)):!K(d)||d.removeAudio||d.replaceAudio||(d.keepAudio=!0),d.addVideo){if(d.keepVideo=!1,d.replaceVideo=!1,d.removeVideo=!1,d.videoSend=!0,c.myStream&&c.myStream.getVideoTracks()&&c.myStream.getVideoTracks().length)return o.error("Can't add video stream, there already is one"),void i.error("Can't add video stream, there already is one")}else d.removeVideo?(d.keepVideo=!1,d.replaceVideo=!1,d.addVideo=!1,d.videoSend=!1):d.replaceVideo&&(d.keepVideo=!1,d.addVideo=!1,d.removeVideo=!1,d.videoSend=!0);null===c.myStream||void 0===c.myStream?(d.replaceVideo&&(d.keepVideo=!1,d.replaceVideo=!1,d.addVideo=!0,d.videoSend=!0),Z(d)&&(d.keepVideo=!1,d.addVideo=!0)):null===c.myStream.getVideoTracks()||void 0===c.myStream.getVideoTracks()||0===c.myStream.getVideoTracks().length?(d.replaceVideo&&(d.keepVideo=!1,d.replaceVideo=!1,d.addVideo=!0,d.videoSend=!0),Z(d)&&(d.keepVideo=!1,d.addVideo=!0)):!Z(d)||d.removeVideo||d.replaceVideo||(d.keepVideo=!0),d.addData&&(d.data=!0)}if(K(d)&&d.keepAudio&&Z(d)&&d.keepVideo)return s.consentDialog(!1),void _(e,a,d,i,c.myStream)}if(d.update&&!c.streamExternal){if(d.removeAudio||d.replaceAudio){if(c.myStream&&c.myStream.getAudioTracks()&&c.myStream.getAudioTracks().length){var u=c.myStream.getAudioTracks()[0];o.log("Removing audio track:",u),c.myStream.removeTrack(u);try{u.stop()}catch(e){}}if(c.pc.getSenders()&&c.pc.getSenders().length){var v=!0;if(d.replaceAudio&&("firefox"===o.webRTCAdapter.browserDetails.browser||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72)&&(v=!1),v)for(var f in c.pc.getSenders()){(u=c.pc.getSenders()[f])&&u.track&&"audio"===u.track.kind&&(o.log("Removing audio sender:",u),c.pc.removeTrack(u))}}}if(d.removeVideo||d.replaceVideo){if(c.myStream&&c.myStream.getVideoTracks()&&c.myStream.getVideoTracks().length){u=c.myStream.getVideoTracks()[0];o.log("Removing video track:",u),c.myStream.removeTrack(u);try{u.stop()}catch(e){}}if(c.pc.getSenders()&&c.pc.getSenders().length){var p=!0;if(d.replaceVideo&&("firefox"===o.webRTCAdapter.browserDetails.browser||"chrome"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=72)&&(p=!1),p)for(var f in c.pc.getSenders()){(u=c.pc.getSenders()[f])&&u.track&&"video"===u.track.kind&&(o.log("Removing video sender:",u),c.pc.removeTrack(u))}}}}if(null!==i.stream&&void 0!==i.stream){var g=i.stream;if(o.log("MediaStream provided by the application"),o.debug(g),d.update&&d.replaceAll&&c.myStream&&c.myStream!==i.stream&&!c.streamExternal){try{var m=c.myStream.getTracks();for(var b in m){var h=m[b];o.log(h),null!=h&&h.stop()}}catch(e){}c.myStream=null}return s.consentDialog(!1),void _(e,a,d,i,g)}if(K(d)||Z(d)){if(!o.isGetUserMediaAvailable())return void i.error("getUserMedia not available");var w={mandatory:{},optional:[]};s.consentDialog(!0);var y=K(d);!0===y&&null!=d&&null!=d&&"object"==typeof d.audio&&(y=d.audio);var S=Z(d);if(!0===S&&null!=d&&null!=d){var k=!0===i.simulcast,C=!0===i.simulcast2;if(!k&&!C||a||void 0!==d.video&&!1!==d.video||(d.video="hires"),d.video&&"screen"!=d.video&&"window"!=d.video)if("object"==typeof d.video)S=d.video;else{var D=0,A=0;if("lowres"===d.video?(A=240,240,D=320):"lowres-16:9"===d.video?(A=180,180,D=320):"hires"===d.video||"hires-16:9"===d.video||"hdres"===d.video?(A=720,720,D=1280):"fhdres"===d.video?(A=1080,1080,D=1920):"4kres"===d.video?(A=2160,2160,D=3840):"stdres"===d.video?(A=480,480,D=640):"stdres-16:9"===d.video?(A=360,360,D=640):(o.log("Default video setting is stdres 4:3"),A=480,480,D=640),o.log("Adding media constraint:",d.video),S={height:{ideal:A},width:{ideal:D}},null!==d.videoDeviceId&&void 0!==d.videoDeviceId){let e={exact:d.videoDeviceId};S.deviceId=e}o.log("Adding video constraint:",S)}else if("screen"===d.video||"window"===d.video){if(d.screenshareFrameRate||(d.screenshareFrameRate=3),navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia)return void navigator.mediaDevices.getDisplayMedia({video:!0}).then(function(r){s.consentDialog(!1),K(d)&&!d.keepAudio?navigator.mediaDevices.getUserMedia({audio:!0,video:!1}).then(function(n){r.addTrack(n.getAudioTracks()[0]),_(e,a,d,i,r)}):_(e,a,d,i,r)},function(e){s.consentDialog(!1),i.error(e)});if("chrome"===o.webRTCAdapter.browserDetails.browser){var R=o.webRTCAdapter.browserDetails.version,I=33;window.navigator.userAgent.match("Linux")&&(I=35),R>=26&&R<=I?t(w={video:{mandatory:{googLeakyBucket:!0,maxWidth:window.screen.width,maxHeight:window.screen.height,minFrameRate:d.screenshareFrameRate,maxFrameRate:d.screenshareFrameRate,chromeMediaSource:"screen"}},audio:K(d)&&!d.keepAudio},n):o.extension.getScreen(function(e,r){if(e)return s.consentDialog(!1),i.error(e);(w={audio:!1,video:{mandatory:{chromeMediaSource:"desktop",maxWidth:window.screen.width,maxHeight:window.screen.height,minFrameRate:d.screenshareFrameRate,maxFrameRate:d.screenshareFrameRate},optional:[{googLeakyBucket:!0},{googTemporalLayeredScreencast:!0}]}}).video.mandatory.chromeMediaSourceId=r,t(w,n,K(d)&&!d.keepAudio)})}else if("firefox"===o.webRTCAdapter.browserDetails.browser){if(!(o.webRTCAdapter.browserDetails.version>=33)){var x=new Error("NavigatorUserMediaError");return x.name="Your version of Firefox does not support screen sharing, please install Firefox 33 (or more recent versions)",s.consentDialog(!1),void i.error(x)}t(w={video:{mozMediaSource:d.video,mediaSource:d.video},audio:K(d)&&!d.keepAudio},function(e,r){if(n(e,r),!e)var t=r.currentTime,i=window.setInterval(function(){r||window.clearInterval(i),r.currentTime==t&&(window.clearInterval(i),r.onended&&r.onended()),t=r.currentTime},500)})}return}}null!=d&&"screen"===d.video||navigator.mediaDevices.enumerateDevices().then(function(r){var n=r.some(function(e){return"audioinput"===e.kind}),t=function(e){if(o.debug("isScreenSendEnabled:",e),null==e)return!1;if("object"!=typeof e.video||"object"!=typeof e.video.mandatory)return!1;var r=e.video.mandatory;if(r.chromeMediaSource)return"desktop"===r.chromeMediaSource||"screen"===r.chromeMediaSource;if(r.mozMediaSource)return"window"===r.mozMediaSource||"screen"===r.mozMediaSource;if(r.mediaSource)return"window"===r.mediaSource||"screen"===r.mediaSource;return!1}(d)||r.some(function(e){return"videoinput"===e.kind}),l=K(d),c=Z(d),u=function(e){return o.debug("isAudioSendRequired:",e),null!=e&&!1!==e.audio&&!1!==e.audioSend&&void 0!==e.failIfNoAudio&&null!==e.failIfNoAudio&&!0===e.failIfNoAudio}(d),v=function(e){return o.debug("isVideoSendRequired:",e),null!=e&&!1!==e.video&&!1!==e.videoSend&&void 0!==e.failIfNoVideo&&null!==e.failIfNoVideo&&!0===e.failIfNoVideo}(d);if(l||c||u||v){var f=!!l&&n,p=!!c&&t;if(!f&&!p)return s.consentDialog(!1),i.error("No capture device found"),!1;if(!f&&u)return s.consentDialog(!1),i.error("Audio capture is required, but no capture device found"),!1;if(!p&&v)return s.consentDialog(!1),i.error("Video capture is required, but no capture device found"),!1}var m={audio:!(!n||d.keepAudio)&&y,video:!(!t||d.keepVideo)&&S};o.debug("getUserMedia constraints",m),m.audio||m.video?navigator.mediaDevices.getUserMedia(m).then(function(r){s.consentDialog(!1),_(e,a,d,i,r)}).catch(function(e){s.consentDialog(!1),i.error({code:e.code,name:e.name,message:e.message})}):(s.consentDialog(!1),_(e,a,d,i,g))}).catch(function(e){s.consentDialog(!1),i.error("enumerateDevices error",e)})}else _(e,a,d,i)}function N(e,r){(r=r||{}).success="function"==typeof r.success?r.success:o.noop,r.error="function"==typeof r.error?r.error:X;var n=r.jsep,t=T[e];if(null==t||null===t.webrtcStuff||void 0===t.webrtcStuff)return o.warn("Invalid handle"),void r.error("Invalid handle");var i=t.webrtcStuff;if(null!=n){if(null===i.pc)return o.warn("Wait, no PeerConnection?? if this is an answer, use createAnswer and not handleRemoteJsep"),void r.error("No PeerConnection: if this is an answer, use createAnswer and not handleRemoteJsep");i.pc.setRemoteDescription(n).then(function(){if(o.log("Remote description accepted!"),i.remoteSdp=n.sdp,i.candidates&&i.candidates.length>0){for(var e in i.candidates){var t=i.candidates[e];o.debug("Adding remote candidate:",t),t&&!0!==t.completed?i.pc.addIceCandidate(t):i.pc.addIceCandidate()}i.candidates=[]}r.success()},r.error)}else r.error("Invalid JSEP")}function F(e,r){var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return o.warn("Invalid handle"),0;var t=r?"remote":"local",i=n.webrtcStuff;return i.volume[t]||(i.volume[t]={value:0}),i.pc.getStats&&"chrome"===o.webRTCAdapter.browserDetails.browser?!r||null!==i.remoteStream&&void 0!==i.remoteStream?r||null!==i.myStream&&void 0!==i.myStream?null===i.volume[t].timer||void 0===i.volume[t].timer?(o.log("Starting "+t+" volume monitor"),i.volume[t].timer=setInterval(function(){i.pc.getStats(function(e){for(var n=e.result(),o=0;o<n.length;o++){var a=n[o];"ssrc"==a.type&&(r&&a.stat("audioOutputLevel")?i.volume[t].value=parseInt(a.stat("audioOutputLevel")):!r&&a.stat("audioInputLevel")&&(i.volume[t].value=parseInt(a.stat("audioInputLevel"))))}})},200),0):i.volume[t].value:(o.warn("Local stream unavailable"),0):(o.warn("Remote stream unavailable"),0):(o.warn("Getting the "+t+" volume unsupported by browser"),0)}function W(e,r){var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return o.warn("Invalid handle"),!0;var t=n.webrtcStuff;return null===t.pc||void 0===t.pc?(o.warn("Invalid PeerConnection"),!0):void 0===t.myStream||null===t.myStream?(o.warn("Invalid local MediaStream"),!0):r?null===t.myStream.getVideoTracks()||void 0===t.myStream.getVideoTracks()||0===t.myStream.getVideoTracks().length?(o.warn("No video track"),!0):!t.myStream.getVideoTracks()[0].enabled:null===t.myStream.getAudioTracks()||void 0===t.myStream.getAudioTracks()||0===t.myStream.getAudioTracks().length?(o.warn("No audio track"),!0):!t.myStream.getAudioTracks()[0].enabled}function U(e,r,n){var t=T[e];if(null==t||null===t.webrtcStuff||void 0===t.webrtcStuff)return o.warn("Invalid handle"),!1;var i=t.webrtcStuff;return null===i.pc||void 0===i.pc?(o.warn("Invalid PeerConnection"),!1):void 0===i.myStream||null===i.myStream?(o.warn("Invalid local MediaStream"),!1):r?null===i.myStream.getVideoTracks()||void 0===i.myStream.getVideoTracks()||0===i.myStream.getVideoTracks().length?(o.warn("No video track"),!1):(i.myStream.getVideoTracks()[0].enabled=!n,!0):null===i.myStream.getAudioTracks()||void 0===i.myStream.getAudioTracks()||0===i.myStream.getAudioTracks().length?(o.warn("No audio track"),!1):(i.myStream.getAudioTracks()[0].enabled=!n,!0)}function J(e){var r=T[e];if(null==r||null===r.webrtcStuff||void 0===r.webrtcStuff)return o.warn("Invalid handle"),"Invalid handle";var n=r.webrtcStuff;return null===n.pc||void 0===n.pc?"Invalid PeerConnection":n.pc.getStats?null===n.bitrate.timer||void 0===n.bitrate.timer?(o.log("Starting bitrate timer (via getStats)"),n.bitrate.timer=setInterval(function(){n.pc.getStats().then(function(e){e.forEach(function(e){if(e){var r=!1;if(("video"===e.mediaType||e.id.toLowerCase().indexOf("video")>-1)&&"inbound-rtp"===e.type&&e.id.indexOf("rtcp")<0?r=!0:"ssrc"!=e.type||!e.bytesReceived||"VP8"!==e.googCodecName&&""!==e.googCodecName||(r=!0),r)if(n.bitrate.bsnow=e.bytesReceived,n.bitrate.tsnow=e.timestamp,null===n.bitrate.bsbefore||null===n.bitrate.tsbefore)n.bitrate.bsbefore=n.bitrate.bsnow,n.bitrate.tsbefore=n.bitrate.tsnow;else{var t=n.bitrate.tsnow-n.bitrate.tsbefore;"safari"==o.webRTCAdapter.browserDetails.browser&&(t/=1e3);var i=Math.round(8*(n.bitrate.bsnow-n.bitrate.bsbefore)/t);"safari"===o.webRTCAdapter.browserDetails.browser&&(i=parseInt(i/1e3)),n.bitrate.value=i+" kbits/sec",n.bitrate.bsbefore=n.bitrate.bsnow,n.bitrate.tsbefore=n.bitrate.tsnow}}})})},1e3),"0 kbits/sec"):n.bitrate.value:(o.warn("Getting the video bitrate unsupported by browser"),"Feature unsupported by browser")}let q;V(e),this.getServer=function(){return c},this.isConnected=function(){return S},this.reconnect=function(e){(e=e||{}).success="function"==typeof e.success?e.success:o.noop,e.error="function"==typeof e.error?e.error:o.noop,e.reconnect=!0,V(e)},this.getSessionId=function(){return k},this.destroy=function(n){!function(n){(n=n||{}).success="function"==typeof n.success?n.success:o.noop;var t=!0;void 0!==n.asyncRequest&&null!==n.asyncRequest&&(t=!0===n.asyncRequest);var s=!0;void 0!==n.notifyDestroyed&&null!==n.notifyDestroyed&&(s=!0===n.notifyDestroyed);var l=!1;void 0!==n.cleanupHandles&&null!==n.cleanupHandles&&(l=!0===n.cleanupHandles);if(o.log("Destroying session "+k+" (async="+t+")"),!S)return o.warn("Is the server down? (connected=false)"),void n.success();if(null==k)return o.warn("No session to destroy"),n.success(),void(s&&e.destroyed());if(l)for(var u in T)E(u,{noRequest:!0});var v={janus:"destroy",transaction:o.randomString(12)};null!=b&&(v.token=b);null!=h&&(v.apisecret=h);if(r){v.session_id=k;var f=function(){for(var e in a)i.removeEventListener(e,a[e]);i.removeEventListener("message",p),i.removeEventListener("error",m),d&&clearTimeout(d),i.close()},p=function(r){var t=JSON.parse(r.data);t.session_id==v.session_id&&t.transaction==v.transaction&&(f(),n.success(),s&&e.destroyed())},m=function(r){f(),n.error("Failed to destroy the server: Is the server down?"),s&&e.destroyed()};return i.addEventListener("message",p),i.addEventListener("error",m),void(1==i.readyState&&i.send(JSON.stringify(v)))}o.httpAPICall(c+"/"+k,{verb:"POST",async:t,withCredentials:g,body:v,success:function(r){o.log("Destroyed session:"),o.debug(r),k=null,S=!1,"success"!==r.janus&&o.error("Ooops: "+r.error.code+" "+r.error.reason),n.success(),s&&e.destroyed()},error:function(r,t){o.error(r+":",t),k=null,S=!1,n.success(),s&&e.destroyed()}})}(n)},this.attach=function(e){!function(e){if((e=e||{}).success="function"==typeof e.success?e.success:o.noop,e.error="function"==typeof e.error?e.error:o.noop,e.consentDialog="function"==typeof e.consentDialog?e.consentDialog:o.noop,e.iceState="function"==typeof e.iceState?e.iceState:o.noop,e.mediaState="function"==typeof e.mediaState?e.mediaState:o.noop,e.webrtcState="function"==typeof e.webrtcState?e.webrtcState:o.noop,e.slowLink="function"==typeof e.slowLink?e.slowLink:o.noop,e.onmessage="function"==typeof e.onmessage?e.onmessage:o.noop,e.onlocalstream="function"==typeof e.onlocalstream?e.onlocalstream:o.noop,e.onremotestream="function"==typeof e.onremotestream?e.onremotestream:o.noop,e.ondata="function"==typeof e.ondata?e.ondata:o.noop,e.ondataopen="function"==typeof e.ondataopen?e.ondataopen:o.noop,e.oncleanup="function"==typeof e.oncleanup?e.oncleanup:o.noop,e.ondetached="function"==typeof e.ondetached?e.ondetached:o.noop,!S)return o.warn("Is the server down? (connected=false)"),void e.error("Is the server down? (connected=false)");var n=e.plugin;if(null==n)return o.error("Invalid plugin"),void e.error("Invalid plugin");var t=e.opaqueId,a=e.token?e.token:b,d=o.randomString(12),s={janus:"attach",plugin:n,opaque_id:t,transaction:d};null!=a&&(s.token=a);null!=h&&(s.apisecret=h);if(r)return A[d]=function(r){if(o.debug(r),"success"!==r.janus)return o.error("Ooops: "+r.error.code+" "+r.error.reason),void e.error("Ooops: "+r.error.code+" "+r.error.reason);var t=r.data.id;o.log("Created handle: "+t);var i={session:C,plugin:n,id:t,token:a,detached:!1,webrtcStuff:{started:!1,myStream:null,streamExternal:!1,remoteStream:null,mySdp:null,mediaConstraints:null,pc:null,dataChannel:{},dtmfSender:null,trickle:!0,iceDone:!1,volume:{value:null,timer:null},bitrate:{value:null,bsnow:null,bsbefore:null,tsnow:null,tsbefore:null,timer:null},stats:null},getId:function(){return t},getPlugin:function(){return n},getVolume:function(){return F(t,!0)},getRemoteVolume:function(){return F(t,!0)},getLocalVolume:function(){return F(t,!1)},isAudioMuted:function(){return W(t,!1)},muteAudio:function(){return U(t,!1,!0)},unmuteAudio:function(){return U(t,!1,!1)},isVideoMuted:function(){return W(t,!0)},muteVideo:function(){return U(t,!0,!0)},unmuteVideo:function(){return U(t,!0,!1)},getBitrate:function(){return J(t)},getStatsInfo:function(e){return function(e,r){var n=T[e];if(null==n||null===n.webrtcStuff||void 0===n.webrtcStuff)return null;var t=n.webrtcStuff;if(null===t.pc||void 0===t.pc)return null;if(t.pc.getStats){return t.pc.getStats().then(function(e){e.forEach(function(e){r.log(e)})}),t.stats}return null}(t,e)},getLocalStats:function(e,r,n){return function(e,r,n,t){var i=T[e];if(null==i||null===i.webrtcStuff||void 0===i.webrtcStuff)return o.warn("Invalid handle"),"Invalid handle";var a=i.webrtcStuff;if(null===a.pc||void 0===a.pc)return"Invalid PeerConnection";a.pc.getStats&&a.pc.getStats().then(function(e){if(n%r==0){let r={};e.forEach(n=>{let t,i;if("outbound-rtp"===n.type){if(n.isRemote)return;const o=n.timestamp;if(t=n.bytesSent,i=n.packetsSent,q&&q.has(n.id)){let i=(o-q.get(n.id).timestamp)/1e3,a=8*(t-q.get(n.id).bytesSent)/i/1e3;if(a=parseInt(a,10),"video"===n.kind||"video"===n.mediaType){r.videoBitrate=a,r.videoBytesSent=t;let o=e.get(n.trackId),d=o.frameWidth,s=o.frameHeight,l=o.framesSent,c=q.get(n.id).trackId,u=(l-q.get(c).framesSent)/i;u=parseInt(u,10),r.frameWidth=d,r.frameHeight=s,r.fps=u}else"audio"!==n.kind&&"audio"!==n.mediaType||(r.audioBitrate=a,r.audioBytesSent=t)}if(G&&G.has(n.id)){let i=(o-G.get(n.id).timestamp)/1e3,a=8*(t-G.get(n.id).bytesSent)/i/1e3;if(a=parseInt(a,10),"video"===n.kind||"video"===n.mediaType){r.videoAverageBitrate=a;let t=e.get(n.trackId).framesSent,o=G.get(n.id).trackId,d=(t-G.get(o).framesSent)/i;d=parseInt(d,10),r.averageFps=d}else"audio"!==n.kind&&"audio"!==n.mediaType||(r.audioAverageBitrate=a)}}else if("candidate-pair"===n.type){let e=1e3*n.currentRoundTripTime;e=parseInt(e,10),r.rtt=e}r.timestamp=parseInt(n.timestamp,10)}),G?t.success(r):t.error(),G=e}else t.error();q=e})}(t,e,r,n)},getRemoteStats:function(e,r,n){return function(e,r,n,t){var i=T[e];if(null==i||null===i.webrtcStuff||void 0===i.webrtcStuff)return o.warn("Invalid handle"),"Invalid handle";var a=i.webrtcStuff;if(null===a.pc||void 0===a.pc)return"Invalid PeerConnection";a.pc.getStats&&a.pc.getStats().then(function(i){if(n%r==0){let r={};i.forEach(n=>{let t,a;if("inbound-rtp"===n.type){const d=n.timestamp;t=n.bytesReceived,a=n.packetsReceived;let s=H[e];if(s&&s.has(n.id)){let e=(d-s.get(n.id).timestamp)/1e3,a=8*(t-s.get(n.id).bytesReceived)/e/1e3;if(a=parseInt(a,10),"video"===n.kind||"video"===n.mediaType){r.videoBitrate=a,r.videoBytesReceived=t;let d=i.get(n.trackId),l=d.framesReceived,c=s.get(n.id).trackId,u=(l-s.get(c).framesReceived)/e;u=parseInt(u,10);let v=d.frameWidth,f=d.frameHeight;if(r.frameWidth=v,r.frameHeight=f,r.fps=u,"safari"!==o.webRTCAdapter.browserDetails.browser){let e=d.jitterBufferDelay,n=d.jitterBufferEmittedCount,t=e/n*1e3;r.jitterBufferDelay=parseInt(e,10),r.jitterBufferEmittedCount=n,r.jitterBufferMs=parseInt(t,10)}}else"audio"!==n.kind&&"audio"!==n.mediaType||(r.audioBitrate=a,r.audioBytesReceived=t)}let l=z[e];if(l&&l.has(n.id)){let e=(d-l.get(n.id).timestamp)/1e3,o=8*(t-l.get(n.id).bytesReceived)/e/1e3;if(o=parseInt(o,10),"video"===n.kind||"video"===n.mediaType){r.videoAverageBitrate=o;let t=i.get(n.trackId).framesReceived,a=l.get(n.id).trackId,d=(t-l.get(a).framesReceived)/e;d=parseInt(d,10),r.averageFps=d}else"audio"!==n.kind&&"audio"!==n.mediaType||(r.audioAverageBitrate=o)}}else if("candidate-pair"===n.type){let e=1e3*n.currentRoundTripTime;e=parseInt(e,10),r.rtt=e}r.timestamp=parseInt(n.timestamp,10)}),z[e]?t.success(r):t.error(),z[e]=i}else t.error();H[e]=i})}(t,e,r,n)},send:function(e){j(t,e)},data:function(e){O(t,e)},dtmf:function(e){B(t,e)},consentDialog:e.consentDialog,iceState:e.iceState,mediaState:e.mediaState,webrtcState:e.webrtcState,slowLink:e.slowLink,onmessage:e.onmessage,createOffer:function(e){L(t,!0,e)},createAnswer:function(e){L(t,!1,e)},handleRemoteJsep:function(e){N(t,e)},onlocalstream:e.onlocalstream,onremotestream:e.onremotestream,ondata:e.ondata,ondataopen:e.ondataopen,oncleanup:e.oncleanup,ondetached:e.ondetached,hangup:function(e){Y(t,!0===e)},detach:function(e){E(t,e)}};T[t]=i,e.success(i)},s.session_id=k,void i.send(JSON.stringify(s));o.httpAPICall(c+"/"+k,{verb:"POST",withCredentials:g,body:s,success:function(r){if(o.debug(r),"success"!==r.janus)return o.error("Ooops: "+r.error.code+" "+r.error.reason),void e.error("Ooops: "+r.error.code+" "+r.error.reason);var t=r.data.id;o.log("Created handle: "+t);var i={session:C,plugin:n,id:t,token:a,detached:!1,webrtcStuff:{started:!1,myStream:null,streamExternal:!1,remoteStream:null,mySdp:null,mediaConstraints:null,pc:null,dataChannel:{},dtmfSender:null,trickle:!0,iceDone:!1,volume:{value:null,timer:null},bitrate:{value:null,bsnow:null,bsbefore:null,tsnow:null,tsbefore:null,timer:null}},getId:function(){return t},getPlugin:function(){return n},getVolume:function(){return F(t,!0)},getRemoteVolume:function(){return F(t,!0)},getLocalVolume:function(){return F(t,!1)},isAudioMuted:function(){return W(t,!1)},muteAudio:function(){return U(t,!1,!0)},unmuteAudio:function(){return U(t,!1,!1)},isVideoMuted:function(){return W(t,!0)},muteVideo:function(){return U(t,!0,!0)},unmuteVideo:function(){return U(t,!0,!1)},getBitrate:function(){return J(t)},send:function(e){j(t,e)},data:function(e){O(t,e)},dtmf:function(e){B(t,e)},consentDialog:e.consentDialog,iceState:e.iceState,mediaState:e.mediaState,webrtcState:e.webrtcState,slowLink:e.slowLink,onmessage:e.onmessage,createOffer:function(e){L(t,!0,e)},createAnswer:function(e){L(t,!1,e)},handleRemoteJsep:function(e){N(t,e)},onlocalstream:e.onlocalstream,onremotestream:e.onremotestream,ondata:e.ondata,ondataopen:e.ondataopen,oncleanup:e.oncleanup,ondetached:e.ondetached,hangup:function(e){Y(t,!0===e)},detach:function(e){E(t,e)}};T[t]=i,e.success(i)},error:function(e,r){o.error(e+":",r)}})}(e)};let G=null;let H=[],z=[];function X(e){o.error("WebRTC error:",e)}function Y(e,n){o.log("Cleaning WebRTC stuff");var t=T[e];if(null!=t){var a=t.webrtcStuff;if(null!=a){if(!0===n){var d={janus:"hangup",transaction:o.randomString(12)};null!==t.token&&void 0!==t.token&&(d.token=t.token),null!=h&&(d.apisecret=h),o.debug("Sending hangup request (handle="+e+"):"),o.debug(d),r?(d.session_id=k,d.handle_id=e,i.send(JSON.stringify(d))):o.httpAPICall(c+"/"+k+"/"+e,{verb:"POST",withCredentials:g,body:d})}a.remoteStream=null,a.volume&&(a.volume.local&&a.volume.local.timer&&clearInterval(a.volume.local.timer),a.volume.remote&&a.volume.remote.timer&&clearInterval(a.volume.remote.timer)),a.volume={},a.bitrate.timer&&clearInterval(a.bitrate.timer),a.bitrate.timer=null,a.bitrate.bsnow=null,a.bitrate.bsbefore=null,a.bitrate.tsnow=null,a.bitrate.tsbefore=null,a.bitrate.value=null;try{if(!a.streamExternal&&null!==a.myStream&&void 0!==a.myStream){o.log("Stopping local stream tracks");var s=a.myStream.getTracks();for(var l in s){var u=s[l];o.log(u),null!=u&&u.stop()}}}catch(e){}a.streamExternal=!1,a.myStream=null;try{a.pc.close()}catch(e){}a.pc=null,a.candidates=null,a.mySdp=null,a.remoteSdp=null,a.iceDone=!1,a.dataChannel={},a.dtmfSender=null}t.oncleanup()}}function K(e){return o.debug("isAudioSendEnabled:",e),null==e||!1!==e.audio&&(void 0===e.audioSend||null===e.audioSend||!0===e.audioSend)}function Q(e){return o.debug("isAudioRecvEnabled:",e),null==e||!1!==e.audio&&(void 0===e.audioRecv||null===e.audioRecv||!0===e.audioRecv)}function Z(e){return o.debug("isVideoSendEnabled:",e),null==e||!1!==e.video&&(void 0===e.videoSend||null===e.videoSend||!0===e.videoSend)}function $(e){return o.debug("isVideoRecvEnabled:",e),null==e||!1!==e.video&&(void 0===e.videoRecv||null===e.videoRecv||!0===e.videoRecv)}}function a(e,r,n){null==n&&(n={}),n.success="function"==typeof n.success?n.success:function(){},n.error="function"==typeof n.error?n.error:function(){};!function(e,r,n,t,i){let o=null;try{o=new XMLHttpRequest}catch(e){try{o=new ActiveXObject("Microsoft.XMLHTTP")}catch(e){return void window.alert("Your browser does not support XMLHTTP!")}}o.open("POST",e,!0);for(let e in n)if(n.hasOwnProperty(e)){let r=n[e];o.setRequestHeader(e,r)}o.send(JSON.stringify(r)),o.onreadystatechange=function(){}}(e,r,{"Content-Type":"application/json"},n.success,n.error)}o.useDefaultDependencies=function(e){var r=e&&e.fetch||fetch,n=e&&e.Promise||Promise,t=e&&e.WebSocket||WebSocket;return{newWebSocket:function(e,r){return new t(e,r)},extension:e&&e.extension||i,isArray:function(e){return Array.isArray(e)},webRTCAdapter:e&&e.adapter||adapter,httpAPICall:function(e,t){var i={method:t.verb,headers:{Accept:"application/json, text/plain, */*"},cache:"no-cache"};"POST"===t.verb&&(i.headers["Content-Type"]="application/json"),void 0!==t.withCredentials&&(i.credentials=!0===t.withCredentials?"include":t.withCredentials?t.withCredentials:"omit"),void 0!==t.body&&(i.body=JSON.stringify(t.body));var a=r(e,i).catch(function(e){return n.reject({message:"Probably a network error, is the server down?",error:e})});if(void 0!==t.timeout){var d=new n(function(e,r){var n=setTimeout(function(){return clearTimeout(n),r({message:"Request timed out",timeout:t.timeout})},t.timeout)});a=n.race([a,d])}return a.then(function(e){return e.ok?typeof t.success==typeof o.noop?e.json().then(function(e){t.success(e)}).catch(function(r){return n.reject({message:"Failed to parse response body",error:r,response:e})}):void 0:n.reject({message:"API call failed",response:e})}).catch(function(e){typeof t.error==typeof o.noop&&t.error(e.message||"<< internal error >>",e)}),a}}},o.useOldDependencies=function(e){var r=e&&e.jQuery||jQuery,n=e&&e.WebSocket||WebSocket;return{newWebSocket:function(e,r){return new n(e,r)},isArray:function(e){return r.isArray(e)},extension:e&&e.extension||i,webRTCAdapter:e&&e.adapter||adapter,httpAPICall:function(e,n){var t=void 0!==n.body?{contentType:"application/json",data:JSON.stringify(n.body)}:{},i=void 0!==n.withCredentials?{xhrFields:{withCredentials:n.withCredentials}}:{};return r.ajax(r.extend(t,i,{url:e,type:n.verb,cache:!1,dataType:"json",async:n.async,timeout:n.timeout,success:function(e){typeof n.success==typeof o.noop&&n.success(e)},error:function(e,r,t){typeof n.error==typeof o.noop&&n.error(r,t)}}))}}},o.noop=function(){},o.dataChanDefaultLabel="JanusDataChannel",o.init=function(e){if((e=e||{}).callback="function"==typeof e.callback?e.callback:o.noop,!0===o.initDone)e.callback();else{if("undefined"!=typeof console&&void 0!==console.log||(console={log:function(){}}),o.trace=o.noop,o.debug=o.noop,o.vdebug=o.noop,o.log=o.noop,o.warn=o.noop,o.error=o.noop,!0===e.debug||"all"===e.debug)o.trace=console.trace.bind(console),o.debug=console.debug.bind(console),o.vdebug=console.debug.bind(console),o.log=console.log.bind(console),o.warn=console.warn.bind(console),o.error=console.error.bind(console);else if(Array.isArray(e.debug))for(var r in e.debug){var n=e.debug[r];switch(n){case"trace":o.trace=console.trace.bind(console);break;case"debug":o.debug=console.debug.bind(console);break;case"vdebug":o.vdebug=console.debug.bind(console);break;case"log":o.log=console.log.bind(console);break;case"warn":o.warn=console.warn.bind(console);break;case"error":o.error=console.error.bind(console);break;default:console.error("Unknown debugging option '"+n+"' (supported: 'trace', 'debug', 'vdebug', 'log', warn', 'error')")}}o.log("Initializing library");var t=e.dependencies||o.useDefaultDependencies();o.isArray=t.isArray,o.webRTCAdapter=t.webRTCAdapter,o.httpAPICall=t.httpAPICall,o.newWebSocket=t.newWebSocket,o.extension=t.extension,o.extension.init(),o.listDevices=function(e,r){e="function"==typeof e?e:o.noop,null==r&&(r={audio:!0,video:!0}),o.isGetUserMediaAvailable()?navigator.mediaDevices.getUserMedia(r).then(function(r){navigator.mediaDevices.enumerateDevices().then(function(n){o.debug(n),e(n);try{var t=r.getTracks();for(var i in t){var a=t[i];null!=a&&a.stop()}}catch(e){}})}).catch(function(r){o.error(r),e([])}):(o.warn("navigator.mediaDevices unavailable"),e([]))},o.attachMediaStream=function(e,r){"chrome"===o.webRTCAdapter.browserDetails.browser?o.webRTCAdapter.browserDetails.version>=52?e.srcObject=r:void 0!==e.src?e.src=URL.createObjectURL(r):o.error("Error attaching stream to element"):e.srcObject=r},o.reattachMediaStream=function(e,r){"chrome"===o.webRTCAdapter.browserDetails.browser?o.webRTCAdapter.browserDetails.version>=52?e.srcObject=r.srcObject:void 0!==e.src?e.src=r.src:o.error("Error reattaching stream to element"):e.srcObject=r.srcObject};var i=["iPad","iPhone","iPod"].indexOf(navigator.platform)>=0?"pagehide":"beforeunload",a=window["on"+i];if(window.addEventListener(i,function(e){for(var r in o.log("Closing window"),o.sessions)null!==o.sessions[r]&&void 0!==o.sessions[r]&&o.sessions[r].destroyOnUnload&&(o.log("Destroying session "+r),o.sessions[r].destroy({asyncRequest:!1,notifyDestroyed:!1}));a&&"function"==typeof a&&a()}),o.safariVp8=!1,"safari"===o.webRTCAdapter.browserDetails.browser&&o.webRTCAdapter.browserDetails.version>=605)if(RTCRtpSender&&RTCRtpSender.getCapabilities&&RTCRtpSender.getCapabilities("video")&&RTCRtpSender.getCapabilities("video").codecs&&RTCRtpSender.getCapabilities("video").codecs.length){for(var r in RTCRtpSender.getCapabilities("video").codecs){var d=RTCRtpSender.getCapabilities("video").codecs[r];if(d&&d.mimeType&&"video/vp8"===d.mimeType.toLowerCase()){o.safariVp8=!0;break}}o.safariVp8?o.log("This version of Safari supports VP8"):o.warn("This version of Safari does NOT support VP8: if you're using a Technology Preview, try enabling the 'WebRTC VP8 codec' setting in the 'Experimental Features' Develop menu")}else{var s=new RTCPeerConnection({},{});s.createOffer({offerToReceiveVideo:!0}).then(function(e){o.safariVp8=-1!==e.sdp.indexOf("VP8"),o.safariVp8?o.log("This version of Safari supports VP8"):o.warn("This version of Safari does NOT support VP8: if you're using a Technology Preview, try enabling the 'WebRTC VP8 codec' setting in the 'Experimental Features' Develop menu"),s.close(),s=null})}o.initDone=!0,e.callback()}},o.isWebrtcSupported=function(){return void 0!==window.RTCPeerConnection&&null!==window.RTCPeerConnection},o.isGetUserMediaAvailable=function(){return void 0!==navigator.mediaDevices&&null!==navigator.mediaDevices&&void 0!==navigator.mediaDevices.getUserMedia&&null!==navigator.mediaDevices.getUserMedia},o.randomString=function(e){for(var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",t=0;t<e;t++){var i=Math.floor(Math.random()*r.length);n+=r.substring(i,i+1)}return n};var d=null;d="wss://rtc.exp.bcelive.com:8989/janus";var s=null,l=null,c="brtc-client-"+o.randomString(12),u=!1,v=null,f=null,p=null,g=[],m=[],b="",h=null,w=null,y=null,S=null,k=null,T=10,C="h264",D=!0,A=!1,R=!1,I=!0,x=!0,V=!0,j=!1,P=!0,M=!0,O=!0,B=1e4,E="",_=!0,L=!1;let N=null;var F=function(){},W=function(){},U=function(){},J=function(){},q=function(){},G=function(){},H=function(){},z=function(e){},X=function(e){},Y=function(e){},K=function(e){},Q=function(e){},Z=function(e,r){};let $={serverUrl:null,defaultOpen:!1,gatherInterval:2,uploadInterval:10},ee=null;var re=function(){},ne=function(){},te=!1;function ie(e,r){e?ne():re()}function oe(e){return new ae.init(e)}var ae={init:function(e){if(!e)return this;if("string"==typeof e){var r=document.getElementById(e.substring(1));return r?(this[0]=r,this.length=1,this.append=function(e){if(null!=r){var n=document.createElement("div"),t=document.createDocumentFragment();n.innerHTML=e;for(var i=0,o=n.children,a=o.length;i<a;i++)t.appendChild(o[0]);return r.appendChild(t),o=null,t=null,n=null,r}},this.remove=function(){null!=r&&null!=r.parentNode&&r.parentNode.removeChild(r)},this.attr=function(e,n){if(null!=r)return r.setAttribute(e,n),this[0]=r,this.length=1,this},this.unbind=function(e){return r},this.empty=function(){return r.innerHTML="",r},this.hide=function(){null!=r&&(r.style.display="none")},this.removeClass=function(e){if(null!=r)return r.classList.remove(e),this[0]=r,this.length=1,this.show=function(){null!=r&&(r.style.display=0)},this.text=function(e){if(null!=r)return r.innerHTML=e,this[0]=r,this.length=1,this.show=function(){null!=r&&(r.style.display=0)},this},this},this.show=function(){null!=r&&("none"===r.style.display?r.style.display="inline-block":r.style.display=0)},this.get=function(e){return r},this.bind=function(e,n){r.addEventListener(e,n)},this.text=function(e){null!=r&&(r.innerHTML=e)}):(this.length=0,this.remove=function(){},this.append=function(e){},this.empty=function(e){},this.text=function(e){},this.get=function(e){return null}),this}}};window.BRTC_Start=function(e){if(console.log("BRTC_Start: "+window.BRTC_Version()),(e=e||{}).success="function"==typeof e.success?e.success:function(){},e.error="function"==typeof e.error?e.error:function(){},e.destroyed="function"==typeof e.destroyed?e.destroyed:function(){},e.onlocalstream="function"==typeof e.onlocalstream?e.onlocalstream:function(){},null!==e.server&&void 0!==e.server&&(d=e.server),null!==e.videocodec&&void 0!==e.videocodec&&(C=e.videocodec),null!==e.aspublisher&&void 0!==e.aspublisher&&(D=e.aspublisher),null!==e.aslistener&&void 0!==e.aslistener&&e.aslistener,null!==e.usingdatachannel&&void 0!==e.usingdatachannel&&(R=e.usingdatachannel),null!==e.usingvideo&&void 0!==e.usingvideo&&(I=e.usingvideo),null!==e.usingaudio&&void 0!==e.usingaudio&&(x=e.usingaudio),null!==e.showvideobps&&void 0!==e.showvideobps&&(V=e.showvideobps),null!==e.autoplaymuted&&void 0!==e.autoplaymuted&&(j=e.autoplaymuted),null!==e.autosubscribe&&void 0!==e.autosubscribe&&(P=e.autosubscribe),null!==e.autopublish&&void 0!==e.autopublish&&(M=e.autopublish),null!==e.sharescreen&&void 0!==e.sharescreen&&(A=e.sharescreen),null!==e.showspinner&&void 0!==e.showspinner&&(O=e.showspinner),null!==e.logintimeout&&void 0!==e.logintimeout&&(B=e.logintimeout),null!==e.logintimeoutevent&&void 0!==e.logintimeoutevent&&(H=e.logintimeoutevent),null!==e.remotevideoloading&&void 0!==e.remotevideoloading&&(z=e.remotevideoloading),null!==e.statsconfig&&void 0!==e.statsconfig){let r=e.statsconfig;null!==r.serverurl&&void 0!==r.serverurl&&($.serverUrl=r.serverurl),null!==r.gatherinterval&&void 0!==r.gatherinterval&&parseInt(r.gatherinterval,10)&&($.gatherInterval=r.gatherinterval),null!==r.uploadinterval&&void 0!==r.uploadinterval&&parseInt(r.uploadinterval,10)&&($.uploadInterval=r.uploadinterval)}null!==e.linkdownupthreshold&&void 0!==e.linkdownupthreshold&&e.linkdownupthreshold,null!==e.linkdownevent&&void 0!==e.linkdownevent&&(re=e.linkdownevent),null!==e.linkupevent&&void 0!==e.linkupevent&&(ne=e.linkupevent),null!==e.mirrorlocalvideo&&void 0!==e.mirrorlocalvideo&&(te=e.mirrorlocalvideo),setTimeout(function(){null===l&&H()},B),null!==e.rtmpserver&&void 0!==e.rtmpserver&&(E=e.rtmpserver),null!==e.rtmpmix&&void 0!==e.rtmpmix&&(_=e.rtmpmix),null!==e.recording&&void 0!==e.recording&&(L=e.recording),window.location.hostname.includes("neolix")&&(j=!0),d=d+"?appid="+e.appid+"&roomname="+e.roomname+"&uid="+e.userid+"&token="+e.token,u||(u=!0,F=e.success,W=e.error,U=e.destroyed,J=e.onlocalstream,null!==e.remotevideoon&&void 0!==e.remotevideoon&&(X=e.remotevideoon),null!==e.remotevideooff&&void 0!==e.remotevideooff&&(Y=e.remotevideooff),null!==e.remotevideocoming&&void 0!==e.remotevideocoming&&(K=e.remotevideocoming),null!==e.remotevideoleaving&&void 0!==e.remotevideoleaving&&(Q=e.remotevideoleaving),null!==e.remotedata&&void 0!==e.remotedata&&(Z=e.remotedata),null!==e.localvideopublishing&&void 0!==e.localvideopublishing&&(q=e.localvideopublishing),null!==e.localvideopublished_ok&&void 0!==e.localvideopublished_ok&&(G=e.localvideopublished_ok),o.isWebrtcSupported()?(w=e.roomname,S=e.userid,h=e.appid,k=e.displayname,null!==e.videodeviceid&&void 0!==e.videodeviceid&&(N=e.videodeviceid),D||(k+=":web_listener"),y="publisher","videoremote1"!=e.remotevideoviewid&&oe("#"+e.remotevideoviewid).append('<div style="width: 100%;height: 100%;" id="videoremote1"></div>'),"videolocal"!=e.localvideoviewid&&oe("#"+e.localvideoviewid).append('<div  id="videolocal"></div>'),"videoremote2"!=e.remotevideoviewid2&&oe("#"+e.remotevideoviewid2).append('<div style="width: 100%;height: 100%;" id="videoremote2"></div>'),"videoremote3"!=e.remotevideoviewid3&&oe("#"+e.remotevideoviewid3).append('<div style="width: 100%;height: 100%;" id="videoremote3"></div>'),"videoremote4"!=e.remotevideoviewid4&&oe("#"+e.remotevideoviewid4).append('<div style="width: 100%;height: 100%;" id="videoremote4"></div>'),"videoremote5"!=e.remotevideoviewid5&&oe("#"+e.remotevideoviewid5).append('<div style="width: 100%;height: 100%;" id="videoremote5"></div>'),o.init({debug:"all",callback:function(){s=new o({server:d,success:function(){s.attach({plugin:"janus.plugin.videoroom",opaqueId:c,success:function(e){var r,n;l=e,o.log("Plugin attached! ("+l.getPlugin()+", id="+l.getId()+")"),o.log("  -- This is a publisher/manager"),r=k,n={request:"create",id:parseInt(S),room_name:w,publishers:500,videocodec:C,app_id:h},l.send({message:n,success:function(e){o.log("create resp data:"+e),T=e.room,o.log("create roomid:"+T);var n={request:"join",room:parseInt(T),room_name:w,ptype:"publisher",id:parseInt(S),app_id:h,role:y,token:"no_token",display:r};r,E.includes("rtmp")&&(n.rtmp=new Object,n.rtmp.url=new Object,n.rtmp.url=E,n.rtmp.mix=_),L&&(n.recording=new Object,n.recording.rec=L),o.log("userid:"+S+",appid:"+h),l.send({message:n})}}),F()},error:function(e){o.error("  -- Error attaching plugin...",e),W("Error attaching plugin... "+e)},consentDialog:function(e){o.debug("Consent dialog should be "+(e?"on":"off")+" now")},slowLink:function(e,r){ie(e)},mediaState:function(e,r){o.log("Janus "+(r?"started":"stopped")+" receiving our "+e)},webrtcState:function(e){o.log("Janus says our WebRTC PeerConnection is "+(e?"up":"down")+" now"),e&&function(e){let r=e.defaultOpen,n=e.serverUrl,t=e.gatherInterval,i=e.uploadInterval;!0===r&&null===n&&(n="http://10.99.200.227:8888/rtc-quality-stats-noAuth");let o=[],d=0,s={app:{sdkVersion:window.BRTC_Version()},userAgent:window.navigator.userAgent};ee=window.setInterval(async function(){let r={timestamp:(new Date).getTime(),type:"stats",appId:h,roomId:T,userId:S,role:y,clientInfo:s,data:{}},c=[];if(r.data.local={},l){let e=new Promise(function(e,n){let i={},o={success(n){i.timestamp=n.timestamp,i.rtt=n.rtt;let t={mediaType:"audio",bitrate:n.audioBitrate,averageBitrate:n.audioAverageBitrate},o={mediaType:"video",bitrate:n.videoBitrate,averageBitrate:n.videoAverageBitrate,fwidth:n.frameWidth,fheight:n.frameHeight,fps:n.fps,averageFps:n.averageFps},a=[t,o];i.senders=a,r.data.local=i,e()},error(){e()}};l.getLocalStats(t,d,o)});c.push(e)}g.length>0&&(r.data.remotes=[]);for(let e=0;e<g.length;e++){let n=g[e];if(n){let e=new Promise(function(e,i){let o={feedId:n.rfid},a={success(n){o.timestamp=n.timestamp,o.rtt=n.rtt;let t={mediaType:"audio",bitrate:n.audioBitrate,averageBitrate:n.audioAverageBitrate},i={mediaType:"video",bitrate:n.videoBitrate,averageBitrate:n.videoAverageBitrate,fwidth:n.frameWidth,fheight:n.frameHeight,fps:n.fps,averageFps:n.averageFps,jitterBufferDelay:n.jitterBufferDelay,jitterBufferEmittedCount:n.jitterBufferEmittedCount,jitterBufferMs:n.jitterBufferMs},a=[t,i];o.receivers=a,r.data.remotes.push(o),e()},error(){e()}};n.getRemoteStats(t,d,a)});c.push(e)}}await Promise.all(c).then(function(s){d>=t&&d%t==0&&(o.push(r),de=r),d>=i&&d%i==0&&(!0!==e.defaultOpen&&null===e.serverUrl||a(n,o),o=[]),c=[],d++})},1e3)}($),G()},onmessage:function(e,r){o.debug(" ::: Got a message (publisher) :::"),o.debug(JSON.stringify(e));var n=e.videoroom;if(o.debug("Event: "+n),null!=n&&null!=n)if("joined"===n){if(v=e.id,p=e.private_id,o.log("Successfully joined room "+e.room+" with ID "+v),M&&ce(!0),void 0!==e.publishers&&null!==e.publishers){var t=e.publishers;for(var i in o.debug("Got a list of available publishers/feeds:"),o.debug(t),t){var a=t[i].id,d=t[i].display;o.debug("  >> ["+a+"] "+d),void 0!==d&&d.includes("web_listener")||(P&&ue(a,d),K(a,d))}}}else if("destroyed"===n)o.warn("The room has been destroyed!"),W("The room has been destroyed");else if("event"===n)if(void 0!==e.publishers&&null!==e.publishers){var t=e.publishers;for(var i in o.debug("Got a list of available publishers/feeds:"),o.debug(t),t){var a=t[i].id,d=t[i].display;o.debug("  >> ["+a+"] "+d),void 0!==d&&d.includes("web_listener")||(P&&ue(a,d),K(a,d))}}else if(void 0!==e.leaving&&null!==e.leaving){var s=e.leaving;o.log("Publisher left: "+s),Q(s);for(var c=null,u=1;u<1e3;u++)if(null!=g[u]&&null!=g[u]&&g[u].rfid==s){c=g[u];break}null!=c&&(o.debug("Feed "+c.rfid+" ("+c.rfdisplay+") has left the room, detaching"),oe("#videoremote"+c.rfindex).empty(),g[c.rfindex]=null,c.detach())}else if(void 0!==e.unpublished&&null!==e.unpublished){var f=e.unpublished;if(o.log("Publisher left: "+f),"ok"===f)return void l.hangup();for(var c=null,u=1;u<1e3;u++)if(null!=g[u]&&null!=g[u]&&g[u].rfid==f){c=g[u];break}null!=c&&(o.debug("Feed "+c.rfid+" ("+c.rfdisplay+") has left the room, detaching"),oe("#videoremote"+c.rfindex).empty(),g[c.rfindex]=null,c.detach())}else void 0!==e.error&&null!==e.error&&W(e.error);null!=r&&(o.debug("Handling SDP as well..."),o.debug(r),l.handleRemoteJsep({jsep:r}))},onlocalstream:function(e,r){if(J(e),o.debug(" ::: Got a local stream :::"),D){f=e,o.debug(JSON.stringify(e)),r&&(oe("#videolocal").empty(),0===oe("#myvideo").length&&(te?oe("#videolocal").append('<video style="display: block; margin: auto; -webkit-transform: scaleX(-1); transform: scaleX(-1);" id="myvideo" width="100%" height="100%" playsinline autoplay muted="muted"/>'):oe("#videolocal").append('<video style="display: block; margin: auto;" id="myvideo" width="100%" height="100%" playsinline autoplay muted="muted"/>')),o.attachMediaStream(oe("#myvideo").get(0),e),oe("#myvideo").get(0).muted="muted",q());var n=e.getVideoTracks();null!=n&&0!==n.length||(oe("#myvideo").hide(),I&&oe("#videolocal").append('<div style="position: relative;"><i style="height: 240px; text-align: center; font-size: 12em !important; height: 100%;"></i><span style="text-align: center; position: absolute; bottom: 0px;right: 0px;left: 0px;font-size: 16px;">No webcam available</span></div>'))}},onremotestream:function(e){},oncleanup:function(){o.log(" ::: Got a cleanup notification: we are unpublished now :::"),f=null}})},error:function(e){o.error(e),W(e)},destroyed:function(){U("destroyed")}})}})):W("No WebRTC support... "))},window.BRTC_Stop=function(){BRTC_StopVideo(),null!=s&&(s.destroy({cleanupHandles:!0}),s=null),null!=ee&&window.clearInterval(ee);for(var e=1;e<1e3;e++)if(null!=g[e]&&null!=g[e]){oe("#videoremote"+g[e].rfindex).remove()}l=null,g=[],m=[],u=!1,H=function(){},oe("#videoremote1").remove(),oe("#videolocal").remove(),oe("#videoremote2").remove(),oe("#videoremote3").remove(),oe("#videoremote4").remove(),oe("#videoremote5").remove()},window.BRTC_SendData=function(e,r){!function(e,r){l.data({label:r,text:e,error:function(e){W(e)}})}(e,r)},window.BRTC_StopVideo=function(){if(!f)return;f.getVideoTracks().forEach(function(e){e.stop()})};let de={};function se(e){l.createOffer({media:{update:!0,replaceVideo:!0,audioRecv:!1,videoRecv:!1,audioSend:!0,videoSend:!0},stream:e,success(e){}})}function le(e){console.log("navigator.MediaDevices.getUserMedia error: ",e.message,e.name)}function ce(e){var r=I,n=!1;e=x,D||(r=!1,e=!1,n=!0),R&&(n=!0);let t={audioRecv:!1,videoRecv:!1,audioSend:e,videoSend:r,data:n,video:A?"screen":"hires"};null!==N&&(t.videoDeviceId=N),null!==l&&l.createOffer({media:t,success:function(t){o.debug("Got publisher SDP!"),o.debug(t);var i={request:"configure",audio:e,video:r,data:n};null!=l&&l.send({message:i,jsep:t})},error:function(r){o.log("WebRTC error:",r),e?ce(!1):W("WebRTC error... "+JSON.stringify(r))}})}function ue(e,r,n){var t=!1;void 0!==n&&(t=!0);var i=null;s.attach({plugin:"janus.plugin.videoroom",opaqueId:c,success:function(r){i=r,o.log("Plugin attached! ("+i.getPlugin()+", id="+i.getId()+")"),o.log("  -- This is a subscriber");var n={request:"join",room:parseInt(T),room_name:w,ptype:"listener",id:parseInt(S),app_id:h,role:y,token:b,feed:e,private_id:p};i.send({message:n})},error:function(e){o.error("  -- Error attaching plugin...",e)},onmessage:function(e,r){o.debug(" ::: Got a message (listener) :::"),o.debug(JSON.stringify(e));var a=e.videoroom;if(o.debug("Event: "+a),null!=a&&null!=a)if("attached"===a){if(t)g[n]=i,i.rfindex=n;else for(var d=1;d<500;d++)if(void 0===g[d]||null===g[d]){g[d]=i,i.rfindex=d;break}if(i.rfid=e.id,i.rfdisplay=e.display,O)if(void 0===i.spinner||null===i.spinner){var s=document.getElementById("videoremote"+i.rfindex);i.spinner=new Spinner({top:100}).spin(s)}else i.spinner.spin();z(i.rfindex),o.log("Successfully attached to feed "+i.rfid+" ("+i.rfdisplay+") in room "+e.room)}else void 0!==e.error&&null!==e.error&&W(e.error);null!=r&&(o.debug("Handling SDP as well..."),o.debug(r),i.createAnswer({jsep:r,media:{audioSend:!1,videoSend:!1,data:!0},success:function(e){o.debug("Got SDP!"),o.debug(e);i.send({message:{request:"start",room:1234},jsep:e})},error:function(e){o.error("WebRTC error:",e),W("WebRTC error... "+JSON.stringify(e))}}))},webrtcState:function(e){o.log("Janus says this WebRTC PeerConnection (feed #"+i.rfindex+") is "+(e?"up":"down")+" now")},onlocalstream:function(e){},slowLink:function(e,r){ie(e)},onremotestream:function(e){if(o.debug("Remote feed #"+i.rfindex),0===oe("#remotevideo"+i.rfindex).length){var r="";j&&(r="muted"),O&&oe("#videoremote"+i.rfindex).append('<video style="border-radius: 5px; display: block; margin: auto;" id="waitingvideo'+i.rfindex+'" width=320 height=240 />'),oe("#videoremote"+i.rfindex).append("<video "+r+' style="rotation: 180deg" id="remotevideo'+i.rfindex+'" width="100%" height="100%" playsinline autoplay/>')}V&&0===oe("#curres"+i.rfindex).length&&oe("#videoremote"+i.rfindex).append('<span id="curres'+i.rfindex+'" style="display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:bold;line-height:1;color:#ffffff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em; background-color: #2fa4e7;  position: absolute; bottom: 0px; left: 0px; margin: 2px;"></span><span id="curbitrate'+i.rfindex+'" style="display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:bold;line-height:1;color:#ffffff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em;background-color: #033c73; position: absolute; bottom: 0px; right: 0px; margin: 2px;"></span>');var n=e.getVideoTracks(),t=oe("#remotevideo"+i.rfindex).get(0);null!==t?(o.attachMediaStream(t,e),null==n||0===n.length?(oe("#remotevideo"+i.rfindex).hide(),oe("#videoremote"+i.rfindex).append('<div sytle="position: relative;" id="novideo'+i.rfindex+'" ><span style="text-align: center; position: absolute; bottom: 0px; right: 0px;left: 0px;font-size: 16px; ">No remote video available</span></div>')):(oe("#remotevideo"+i.rfindex).show(),oe("#remotevideo"+i.rfindex).bind("playing",function(){0!==oe("#novideo"+i.rfindex).length&&oe("#novideo"+i.rfindex).remove(),X(P?i.rfindex:i.rfid),O&&(void 0!==i.spinner&&null!==i.spinner&&i.spinner.stop(),i.spinner=null,oe("#waitingvideo"+i.rfindex).remove()),oe("#remotevideo"+i.rfindex).removeClass("hide");var e=this.videoWidth,r=this.videoHeight;oe("#curres"+i.rfindex).removeClass("hide").text(e+"x"+r).show(),"firefox"===adapter.browserDetails.browser&&setTimeout(function(){var e=oe("#remotevideo"+i.rfindex).get(0).videoWidth,r=oe("#remotevideo"+i.rfindex).get(0).videoHeight;oe("#curres"+i.rfindex).removeClass("hide").text(e+"x"+r).show()},2e3)})),"chrome"!==adapter.browserDetails.browser&&"firefox"!==adapter.browserDetails.browser||(oe("#curbitrate"+i.rfindex).removeClass("hide").show(),m[i.rfindex]=setInterval(function(){if(null!==i){var e=i.getBitrate();oe("#curbitrate"+i.rfindex).text(e)}},1e3))):o.log("no video label for Remote feed #"+i.rfindex)},ondataopen:function(e){o.log("The DataChannel is available!"+e)},ondata:function(e,r){Z(e,r),o.log("The DataChannel on data!"+r+e)},oncleanup:function(){o.log(" ::: Got a cleanup notification (remote feed "+e+") :::"),Y(P?i.rfindex:i.rfid),O&&(void 0!==i.spinner&&null!==i.spinner&&i.spinner.stop(),i.spinner=null,oe("#waitingvideo"+i.rfindex).remove()),oe("#curbitrate"+i.rfindex).remove(),oe("#curres"+i.rfindex).remove(),null!==m[i.rfindex]&&null!==m[i.rfindex]&&clearInterval(m[i.rfindex]),m[i.rfindex]=null,i=null}})}window.BRTC_GetStatsData=function(){return de},window.BRTC_ReplaceVideo=function(e){if(console.log("videoDeviceId: "+e),null==l)return;let r=null,n={audio:!1,video:!0};null!=e&&(n={audio:!1,video:{deviceId:{exact:e}}}),BRTC_StopVideo(),navigator.mediaDevices.getUserMedia(n).then(function(e){se(r=e)}).catch(le)},window.BRTC_ReplaceStream=function(e){console.log("newStream: "+e),null!=l&&null!=e&&se(e)},window.BRTC_GetVideoDevices=function(e){e.success="function"==typeof e.success?e.success:o.noop,e.error="function"==typeof e.error?e.error:o.noop;let r=[];navigator.mediaDevices.enumerateDevices().then(function(n){n.forEach(function(e){"videoinput"===e.kind&&r.push(e)}),e.success(r)}).catch(function(r){e.error(r)})},window.BRTC_GetUserList=function(e){!function(e){null!=e&&(e.success="function"==typeof e.success?e.success:o.noop,e.error="function"==typeof e.error?e.error:o.noop);var r={request:"listparticipants",room:T};l.send({message:r,success:null!=e?e.success:o.noop,error:null!=e?e.error:o.noop})}(e)},window.BRTC_SubscribeStreaming=function(e,r){!function(e,r){for(var n=666,t=500;t<1e3;t++)if(void 0===g[t]||null===g[t]){n=t;break}oe("#"+e).append('<div style="width: 100%;height: 100%;" id="videoremote'+n+'"></div>'),ue(r,"out",n)}(e,r)},window.BRTC_StopSubscribeStreaming=function(e){!function(e){for(var r=1;r<1e3;r++)if(null!=g[r]&&null!=g[r]&&e==g[r].rfid){let e=g[r];g[r]=null,e.detach(),oe("#videoremote"+e.rfindex).remove();break}}(e)},window.BRTC_StartPublish=function(){ce(!0)},window.BRTC_StopPublish=function(){l.send({message:{request:"unpublish"}})},window.BRTC_MuteMicphone=function(e){null!==l&&(e?l.muteAudio():l.unmuteAudio())},window.BRTC_MuteCamera=function(e){null!==l&&(e?l.muteVideo():l.unmuteVideo())},window.BRTC_Version=function(){return"BRTC SDK V0.7.1"}}]);
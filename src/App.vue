<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted } from 'vue'
import { isMobile } from './utils/mobile'

onMounted(() => {
  // 添加移动端类名
  if (isMobile()) {
    document.body.classList.add('is-mobile')
  } else {
    document.body.classList.add('is-desktop')
  }
})
</script>

<template>
  <div class="app-container mobile-container">
    <RouterView />
  </div>
</template>

<style scoped>
.app-container {
  min-height: 100vh;
  background-color: var(--background-base);
  position: relative;
  overflow-x: hidden;

  /* 移动端优化 */
  @media (max-width: 768px) {
    padding-left: var(--safe-area-left);
    padding-right: var(--safe-area-right);
  }
}


</style>



<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header" @click="toggleExpanded">
      <span class="monitor-title">性能监控</span>
      <span class="monitor-score" :class="getScoreClass(score)">{{ score }}分</span>
      <span class="toggle-icon">{{ isExpanded ? '▼' : '▶' }}</span>
    </div>
    
    <div v-if="isExpanded" class="monitor-content">
      <!-- 核心指标 -->
      <div class="metrics-section">
        <h4>核心指标</h4>
        <div class="metric-item">
          <span class="metric-label">页面加载时间:</span>
          <span class="metric-value">{{ formatTime(metrics.loadTime) }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">首次内容绘制 (FCP):</span>
          <span class="metric-value">{{ formatTime(metrics.fcp) }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">最大内容绘制 (LCP):</span>
          <span class="metric-value">{{ formatTime(metrics.lcp) }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">首次输入延迟 (FID):</span>
          <span class="metric-value">{{ formatTime(metrics.fid) }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">累积布局偏移 (CLS):</span>
          <span class="metric-value">{{ formatCLS(metrics.cls) }}</span>
        </div>
      </div>
      
      <!-- 内存使用 -->
      <div v-if="metrics.memory" class="metrics-section">
        <h4>内存使用</h4>
        <div class="metric-item">
          <span class="metric-label">已使用:</span>
          <span class="metric-value">{{ metrics.memory.used }}MB</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">总计:</span>
          <span class="metric-value">{{ metrics.memory.total }}MB</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">限制:</span>
          <span class="metric-value">{{ metrics.memory.limit }}MB</span>
        </div>
      </div>
      
      <!-- 网络状态 -->
      <div v-if="networkInfo" class="metrics-section">
        <h4>网络状态</h4>
        <div class="metric-item">
          <span class="metric-label">连接状态:</span>
          <span class="metric-value">{{ isOnline ? '在线' : '离线' }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">连接类型:</span>
          <span class="metric-value">{{ networkInfo.effectiveType || '未知' }}</span>
        </div>
        <div class="metric-item">
          <span class="metric-label">下行速度:</span>
          <span class="metric-value">{{ networkInfo.downlink || 0 }}Mbps</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div class="monitor-actions">
        <button @click="refreshMetrics" class="action-btn">刷新数据</button>
        <button @click="exportMetrics" class="action-btn">导出数据</button>
        <button @click="hideMonitor" class="action-btn">隐藏监控</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { usePerformanceMonitor, useNetworkMonitor } from '../composables/usePerformance'

const showMonitor = ref(false)
const isExpanded = ref(false)

const {
  metrics,
  collectMetrics,
  getPerformanceScore
} = usePerformanceMonitor()

const {
  networkInfo,
  isOnline
} = useNetworkMonitor()

const score = ref(0)

// 格式化时间
const formatTime = (time?: number) => {
  if (!time) return '0ms'
  return time < 1000 ? `${Math.round(time)}ms` : `${(time / 1000).toFixed(2)}s`
}

// 格式化CLS
const formatCLS = (cls?: number) => {
  if (!cls) return '0'
  return cls.toFixed(3)
}

// 获取分数样式类
const getScoreClass = (score: number) => {
  if (score >= 90) return 'score-excellent'
  if (score >= 70) return 'score-good'
  if (score >= 50) return 'score-fair'
  return 'score-poor'
}

// 切换展开状态
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

// 刷新指标
const refreshMetrics = async () => {
  await collectMetrics()
  score.value = getPerformanceScore(metrics.value)
}

// 导出指标
const exportMetrics = () => {
  const data = {
    timestamp: new Date().toISOString(),
    metrics: metrics.value,
    networkInfo: networkInfo.value,
    score: score.value
  }
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-metrics-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

// 隐藏监控
const hideMonitor = () => {
  showMonitor.value = false
}

// 初始化
onMounted(async () => {
  // 开发环境或特定条件下显示监控
  if (import.meta.env.DEV || localStorage.getItem('show-performance-monitor')) {
    showMonitor.value = true
    await refreshMetrics()
  }
})

// 暴露方法给外部调用
defineExpose({
  show: () => { showMonitor.value = true },
  hide: () => { showMonitor.value = false },
  refresh: refreshMetrics
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  font-size: 12px;
  z-index: 9999;
  min-width: 250px;
  max-width: 350px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.monitor-header {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px 8px 0 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  user-select: none;
}

.monitor-title {
  font-weight: bold;
}

.monitor-score {
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: bold;
}

.score-excellent { background: #4caf50; }
.score-good { background: #ff9800; }
.score-fair { background: #f44336; }
.score-poor { background: #9c27b0; }

.toggle-icon {
  font-size: 10px;
}

.monitor-content {
  padding: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.metrics-section {
  margin-bottom: 16px;
}

.metrics-section h4 {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #ffd700;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 4px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  padding: 2px 0;
}

.metric-label {
  color: #ccc;
}

.metric-value {
  color: #fff;
  font-weight: bold;
}

.monitor-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 4px;
  color: white;
  font-size: 11px;
  cursor: pointer;
  transition: background 0.2s;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .performance-monitor {
    top: 10px;
    right: 10px;
    left: 10px;
    min-width: auto;
    max-width: none;
  }
  
  .monitor-content {
    max-height: 300px;
  }
}
</style>

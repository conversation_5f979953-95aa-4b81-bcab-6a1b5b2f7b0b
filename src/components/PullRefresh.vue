<template>
  <div class="pull-refresh-container" ref="container">
    <!-- 下拉刷新指示器 -->
    <div 
      class="pull-refresh-indicator"
      :class="{
        'is-pulling': isPulling,
        'can-refresh': canRefresh,
        'is-refreshing': isRefreshing
      }"
      :style="{ height: `${Math.max(pullDistance, isRefreshing ? 60 : 0)}px` }"
    >
      <div class="indicator-content">
        <div class="indicator-icon" :class="{ 'rotating': isRefreshing }">
          {{ getStatusIcon(isRefreshing, canRefresh, isPulling) }}
        </div>
        <div class="indicator-text">
          {{ getStatusText(isRefreshing, canRefresh, isPulling) }}
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="pull-refresh-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePullRefresh, usePullRefreshText } from '../composables/usePullRefresh'

interface Props {
  onRefresh?: () => Promise<void>
  threshold?: number
  maxDistance?: number
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 60,
  maxDistance: 120,
  disabled: false
})

const {
  container,
  isRefreshing,
  isPulling,
  pullDistance,
  canRefresh
} = usePullRefresh({
  threshold: props.threshold,
  maxDistance: props.maxDistance,
  onRefresh: props.onRefresh,
  disabled: props.disabled
})

const { getStatusText, getStatusIcon } = usePullRefreshText()
</script>

<style scoped>
.pull-refresh-container {
  position: relative;
  overflow: hidden;
  height: 100%;
}

.pull-refresh-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-lighter);
  transform: translateY(-100%);
  transition: transform 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.pull-refresh-indicator.is-pulling {
  transform: translateY(calc(-100% + var(--pull-distance, 0px)));
}

.pull-refresh-indicator.can-refresh {
  background: var(--success-color);
  color: white;
}

.pull-refresh-indicator.is-refreshing {
  transform: translateY(0);
}

.indicator-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3);
}

.indicator-icon {
  font-size: var(--font-size-lg);
  transition: transform 0.3s ease;
}

.indicator-icon.rotating {
  animation: rotate 1s linear infinite;
}

.indicator-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.pull-refresh-content {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .pull-refresh-indicator {
    backdrop-filter: blur(10px);
  }
  
  .indicator-content {
    padding: var(--space-4);
  }
  
  .indicator-icon {
    font-size: var(--font-size-xl);
  }
  
  .indicator-text {
    font-size: var(--font-size-base);
  }
}
</style>

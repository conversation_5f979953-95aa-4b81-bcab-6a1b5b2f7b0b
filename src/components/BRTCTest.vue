<template>
  <div class="brtc-test">
    <van-cell-group>
      <van-cell title="BRTC SDK状态" :value="sdkStatus" />
      <van-cell title="SDK版本" :value="sdkVersion" />
      <van-cell title="连接状态" :value="connectionStatus" />
    </van-cell-group>

    <div class="test-buttons">
      <van-button 
        type="primary" 
        block 
        @click="testSDKLoaded"
        :loading="testing"
      >
        测试SDK加载
      </van-button>
      
      <van-button
        type="success"
        block
        @click="initBRTCWithAPI"
        :disabled="!sdkLoaded || connecting"
        :loading="connecting"
      >
        初始化BRTC (使用API配置)
      </van-button>
      
      <van-button 
        type="warning" 
        block 
        @click="stopBRTC"
        :disabled="!connected"
      >
        停止BRTC
      </van-button>
    </div>

    <div class="logs">
      <van-cell title="日志" />
      <div class="log-content">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          {{ log }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { showToast } from 'vant'

// 状态
const sdkLoaded = ref(false)
const connected = ref(false)
const testing = ref(false)
const connecting = ref(false)
const sdkVersion = ref('未知')
const logs = ref<string[]>([])

// 计算属性
const sdkStatus = computed(() => sdkLoaded.value ? '已加载' : '未加载')
const connectionStatus = computed(() => {
  if (connecting.value) return '连接中...'
  if (connected.value) return '已连接'
  return '未连接'
})

// 添加日志
const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift(`[${timestamp}] ${message}`)
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 测试SDK加载
const testSDKLoaded = async () => {
  testing.value = true
  addLog('开始测试SDK加载...')
  
  try {
    // 检查全局BRTC函数
    const brtcFunctions = [
      'BRTC_Start',
      'BRTC_Stop', 
      'BRTC_Version',
      'BRTC_GetVideoDevices',
      'BRTC_SendMessageToUser'
    ]
    
    const loadedFunctions = brtcFunctions.filter(fn => typeof window[fn] === 'function')
    
    if (loadedFunctions.length > 0) {
      sdkLoaded.value = true
      addLog(`SDK已加载，找到${loadedFunctions.length}个函数`)
      addLog(`可用函数: ${loadedFunctions.join(', ')}`)
      
      // 获取版本
      if (window.BRTC_Version) {
        try {
          sdkVersion.value = window.BRTC_Version()
          addLog(`SDK版本: ${sdkVersion.value}`)
        } catch (e) {
          addLog('获取版本失败: ' + e)
        }
      }
      
      showToast('SDK加载成功')
    } else {
      sdkLoaded.value = false
      addLog('SDK未加载或函数不可用')
      showToast('SDK未加载')
    }
  } catch (error) {
    addLog('测试失败: ' + error)
    showToast('测试失败')
  } finally {
    testing.value = false
  }
}

// 使用API配置初始化BRTC
const initBRTCWithAPI = async () => {
  if (!sdkLoaded.value) {
    showToast('请先测试SDK加载')
    return
  }

  connecting.value = true
  addLog('开始使用API配置初始化BRTC...')

  try {
    // 先获取API配置
    addLog('正在获取API配置...')
    const response = await fetch('/api/livingRoom/initLivingRoom', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_name: 'cmhk'
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const apiConfig = await response.json()
    addLog('API配置获取成功: ' + JSON.stringify(apiConfig, null, 2))

    // 使用API配置初始化BRTC
    const config = {
      server: apiConfig.url,
      appid: apiConfig.appId,
      token: apiConfig.userToken,
      roomname: apiConfig.roomName,
      userid: apiConfig.userId,
      displayname: 'BRTC测试用户',
      remotevideoviewid: 'remote-video-container',
      usingvideo: false,
      usingaudio: true,
      autopublish: false,
      autosubscribe: true,
      success: () => {
        connected.value = true
        connecting.value = false
        addLog('BRTC初始化成功')
        showToast('连接成功')
      },
      error: (error: any) => {
        connected.value = false
        connecting.value = false
        addLog('BRTC初始化失败: ' + (error?.message || error))
        showToast('连接失败')
      },
      destroyed: () => {
        connected.value = false
        addLog('BRTC连接已断开')
      }
    }

    addLog('调用BRTC_Start...')
    addLog('配置参数: ' + JSON.stringify(config, null, 2))
    window.BRTC_Start(config)

  } catch (error) {
    connecting.value = false
    addLog('初始化异常: ' + error)
    showToast('初始化异常')
  }
}

// 停止BRTC
const stopBRTC = () => {
  try {
    if (window.BRTC_Stop) {
      window.BRTC_Stop()
      connected.value = false
      addLog('BRTC已停止')
      showToast('已断开连接')
    }
  } catch (error) {
    addLog('停止失败: ' + error)
  }
}

onMounted(() => {
  addLog('组件已加载')
  // 自动测试SDK加载
  setTimeout(() => {
    testSDKLoaded()
  }, 1000)
})
</script>

<style scoped>
.brtc-test {
  padding: 16px;
}

.test-buttons {
  margin: 16px 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.logs {
  margin-top: 16px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 12px;
  border-radius: 8px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin-bottom: 4px;
  word-break: break-all;
}
</style>

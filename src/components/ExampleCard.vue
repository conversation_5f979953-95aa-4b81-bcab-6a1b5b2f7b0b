<template>
  <el-card class="example-card" :shadow="shadow">
    <template #header v-if="title">
      <div class="card-header">
        <h3>{{ title }}</h3>
        <slot name="header-actions"></slot>
      </div>
    </template>
    
    <div class="card-content">
      <slot></slot>
    </div>
    
    <template #footer v-if="$slots.footer">
      <div class="card-footer">
        <slot name="footer"></slot>
      </div>
    </template>
  </el-card>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  shadow?: 'always' | 'hover' | 'never'
}

withDefaults(defineProps<Props>(), {
  shadow: 'hover'
})
</script>

<style scoped>
.example-card {
  margin-bottom: 1rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.card-content {
  line-height: 1.6;
  color: var(--text-regular);
}

.card-footer {
  padding-top: 1rem;
  border-top: 1px solid var(--border-light);
}
</style>

<script setup lang="ts">
import { useInfiniteScroll, useInfiniteScrollText } from '../composables/useInfiniteScroll'
import { withDefaults, defineProps, defineExpose} from 'vue'

interface Props {
  onLoad?: () => Promise<void>
  threshold?: number
  disabled?: boolean
  immediate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  threshold: 100,
  disabled: false,
  immediate: true
})

const {
  container,
  isLoading,
  isFinished,
  hasError,
  loadMore
} = useInfiniteScroll({
  threshold: props.threshold,
  onLoad: props.onLoad,
  disabled: props.disabled,
  immediate: props.immediate
})

const { getStatusText, getStatusIcon } = useInfiniteScrollText()

// 处理重试
const handleRetry = () => {
  if (hasError.value) {
    loadMore()
  }
}

// 暴露方法给父组件
defineExpose({
  loadMore,
  finish: () => {
    isFinished.value = true
  },
  reset: () => {
    isLoading.value = false
    isFinished.value = false
    hasError.value = false
  }
})
</script>

<template>
  <div class="infinite-scroll-container" ref="container">
    <!-- 内容区域 -->
    <div class="infinite-scroll-content">
      <slot/>
    </div>
    
    <!-- 加载更多指示器 -->
    <div 
      class="infinite-scroll-indicator"
      :class="{
        'is-loading': isLoading,
        'is-finished': isFinished,
        'has-error': hasError
      }"
      v-if="isLoading || isFinished || hasError"
      @click="handleRetry"
    >
      <div class="indicator-content">
        <div class="indicator-icon" :class="{ 'rotating': isLoading }">
          {{ getStatusIcon(isLoading, isFinished, hasError) }}
        </div>
        <div class="indicator-text">
          {{ getStatusText(isLoading, isFinished, hasError) }}
        </div>
      </div>
    </div>
  </div>
</template>


<style scoped>
.infinite-scroll-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.infinite-scroll-content {
  min-height: 100%;
}

.infinite-scroll-indicator {
  padding: var(--space-4);
  text-align: center;
  background: var(--background-white);
  border-top: 1px solid var(--border-lighter);
  transition: var(--transition-base);
}

.infinite-scroll-indicator.has-error {
  cursor: pointer;
  background: var(--danger-light);
  color: var(--danger-color);
}

.infinite-scroll-indicator.has-error:hover {
  background: var(--danger-color);
  color: white;
}

.infinite-scroll-indicator.is-finished {
  background: var(--background-base);
  color: var(--text-secondary);
}

.indicator-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
}

.indicator-icon {
  font-size: var(--font-size-lg);
  transition: transform 0.3s ease;
}

.indicator-icon.rotating {
  animation: rotate 1s linear infinite;
}

.indicator-text {
  font-size: var(--font-size-sm);
  font-weight: 500;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .infinite-scroll-indicator {
    padding: var(--space-5);
    min-height: var(--mobile-touch-target);
  }
  
  .indicator-icon {
    font-size: var(--font-size-xl);
  }
  
  .indicator-text {
    font-size: var(--font-size-base);
  }
}
</style>

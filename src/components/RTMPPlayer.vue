<template>
  <div class="rtmp-player">
    <!-- 静态视频替代方案 -->
    <video
      ref="videoRef"
      class="video-element"
      :autoplay="autoplay"
      :muted="muted"
      :controls="controls"
      :loop="true"
      playsinline
      webkit-playsinline
      x5-playsinline
      @loadeddata="onVideoReady"
      @play="onVideoPlay"
      @error="onVideoError"
    >
      <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4">
      您的浏览器不支持视频播放
    </video>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <van-loading type="spinner" color="#ffffff" size="24px">
        正在加载...
      </van-loading>
      <p class="loading-text">RTMP流暂时使用演示视频替代</p>
    </div>
    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { showToast } from 'vant'

interface Props {
  rtmpUrl: string
  autoplay?: boolean
  muted?: boolean
  controls?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoplay: true,
  muted: true,
  controls: false
})

const emit = defineEmits<{
  ready: []
  play: []
  error: [error: string]
}>()

// 响应式数据
const videoRef = ref<HTMLVideoElement>()
const isLoading = ref(true)
const hasError = ref(false)
const errorMessage = ref('')

// 视频事件处理
const onVideoReady = () => {
  console.log('✅ 演示视频准备就绪')
  isLoading.value = false
  emit('ready')
}

const onVideoPlay = () => {
  console.log('▶️ 开始播放演示视频')
  emit('play')
}

const onVideoError = () => {
  console.error('❌ 演示视频播放错误')
  handleError('演示视频播放失败')
}

// 错误处理
const handleError = (message: string) => {
  hasError.value = true
  errorMessage.value = message
  isLoading.value = false
  emit('error', message)
  console.error('🚨 播放器错误:', message)
}

// 重试播放
const retry = () => {
  console.log('🔄 重试播放')
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''
  
  if (videoRef.value) {
    videoRef.value.load()
  }
}

// 在VLC中打开RTMP
const openInVLC = () => {
  const vlcUrl = `vlc://${props.rtmpUrl}`
  window.open(vlcUrl, '_blank')
  
  navigator.clipboard?.writeText(props.rtmpUrl).then(() => {
    showToast('RTMP地址已复制，请在VLC中播放')
  }).catch(() => {
    showToast('请手动复制RTMP地址到VLC播放器')
  })
}

// 生命周期
onMounted(() => {
  console.log('🚀 RTMP播放器已加载，使用演示视频')
  console.log('RTMP URL:', props.rtmpUrl)
  
  // 设置超时检查
  setTimeout(() => {
    if (isLoading.value) {
      handleError('演示视频加载超时')
    }
  }, 10000)
})

// 暴露方法
defineExpose({
  retry,
  openInVLC
})
</script>

<style scoped>
.rtmp-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
}

.video-element {
  width: 100%;
  height: 100%;
  background: #000;
  object-fit: cover;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  z-index: 10;
  padding: 20px;
}

.loading-text {
  margin-top: 10px;
  font-size: 12px;
  opacity: 0.8;
}

.error-content {
  text-align: center;
  max-width: 500px;
}

.error-message {
  margin: 12px 0;
  font-size: 14px;
  color: #ff4d4f;
}

.action-buttons {
  margin: 15px 0;
}

.action-buttons .van-button {
  margin: 0 5px;
}

.help-text {
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  font-size: 12px;
  text-align: left;
}

/* 移动端优化 */
@media (max-width: 767px) {
  .error-message {
    font-size: 12px;
  }
  
  .action-buttons .van-button {
    font-size: 11px;
    padding: 4px 8px;
    margin: 2px;
  }
  
  .help-text {
    font-size: 11px;
  }
}
</style>

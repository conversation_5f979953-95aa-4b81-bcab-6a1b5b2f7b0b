/**
 * 百度RTC配置文件
 * 
 * ⚠️ 重要提示：
 * 1. 请到百度智能云控制台获取真实的AppID和Token
 * 2. 将下面的示例值替换为您的真实配置
 * 3. 不要将真实的Token提交到版本控制系统中
 */

export interface BRTCConfig {
  server: string
  appid: string
  token: string
  roomname?: string
  userid?: string
  displayname?: string
  remotevideoviewid?: string  // 显示远端视频的DOM元素ID
}

// 默认配置（示例值，需要替换）
export const DEFAULT_BRTC_CONFIG: BRTCConfig = {
  server: 'wss://rtc.exp.bcelive.com/janus',
  appid: 'apppc7fpsryj72g',  // ⚠️ 请替换为真实的百度RTC AppID
  token: '004ea275153435d6db52165d64ae49dc83e7bc5c3ea1754272451f54066071754272511',  // ⚠️ 请替换为真实的百度RTC Token
  roomname: '6a3c5875-149a-419b-9379-17607f9e8b6',
  userid: '405968177',  // 将在运行时生成
  displayname: 'VoiceDemo',
  remotevideoviewid: 'remote-video-container'  // 远端视频容器ID
}

// 检查配置是否有效
export const isValidBRTCConfig = (config: BRTCConfig): boolean => {
  return config.appid !== 'apppc7fpsryj72g' && 
         config.token !== '004ea275153435d6db52165d64ae49dc83e7bc5c3ea1754272451f54066071754272511' &&
         config.appid !== 'apppc7fpsryj72g' &&  // 旧的示例值
         config.appid.length > 0 &&
         config.token.length > 0
}


// 环境变量配置（可选）
export const getBRTCConfigFromEnv = (): Partial<BRTCConfig> => {
  return {
    appid: import.meta.env.VITE_BRTC_APPID || DEFAULT_BRTC_CONFIG.appid,
    token: import.meta.env.VITE_BRTC_TOKEN || DEFAULT_BRTC_CONFIG.token,
    server: import.meta.env.VITE_BRTC_SERVER || DEFAULT_BRTC_CONFIG.server
  }
}

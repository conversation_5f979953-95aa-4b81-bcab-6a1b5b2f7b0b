/**
 * 百度ASR配置文件
 * 请在这里配置您的百度语音识别API密钥
 */

import type { BaiduASRConfig } from '@/utils/baiduASR'

// 百度ASR API配置
// 请到 https://ai.baidu.com 控制台获取您的API密钥
export const BAIDU_ASR_CONFIG: BaiduASRConfig = {
  // 应用ID - 请替换为您的实际AppID
  appId: import.meta.env.VITE_BAIDU_APP_ID || 'your_app_id_here',

  // API Key - 请替换为您的实际API Key
  apiKey: import.meta.env.VITE_BAIDU_API_KEY || 'your_api_key_here',

  // Secret Key - 请替换为您的实际Secret Key
  secretKey: import.meta.env.VITE_BAIDU_SECRET_KEY || 'your_secret_key_here',

  // 音频格式，推荐使用pcm
  format: 'pcm',

  // 采样率，百度ASR要求16000
  rate: 16000,

  // 声道数，单声道
  channel: 1
}

// 开发环境配置
export const DEV_CONFIG: BaiduASRConfig = {
  ...BAIDU_ASR_CONFIG,
  // 开发环境临时配置，避免未配置API密钥时报错
  appId: import.meta.env.VITE_BAIDU_APP_ID || 'dev_app_id_placeholder',
  apiKey: import.meta.env.VITE_BAIDU_API_KEY || 'dev_api_key_placeholder',
  secretKey: import.meta.env.VITE_BAIDU_SECRET_KEY || 'dev_secret_key_placeholder'
}

// 生产环境配置
export const PROD_CONFIG: BaiduASRConfig = {
  ...BAIDU_ASR_CONFIG,
  // 生产环境必须使用正式密钥
}

/**
 * 获取当前环境的配置
 */
export function getBaiduASRConfig(): BaiduASRConfig {
  const isDev = import.meta.env.DEV
  
  if (isDev) {
    console.log('使用开发环境百度ASR配置')
    return DEV_CONFIG
  } else {
    console.log('使用生产环境百度ASR配置')
    return PROD_CONFIG
  }
}

/**
 * 验证配置是否有效
 */
export function validateConfig(config: BaiduASRConfig): boolean {
  const requiredFields = ['appId', 'apiKey', 'secretKey']
  const isDev = import.meta.env.DEV

  for (const field of requiredFields) {
    const value = config[field as keyof BaiduASRConfig] as string

    // 检查是否为空或默认值
    if (!value || value === `your_${field.toLowerCase()}_here`) {
      if (isDev) {
        console.warn(`百度ASR配置警告: ${field} 未设置，开发环境将使用占位符`)
        continue
      } else {
        console.error(`百度ASR配置错误: ${field} 未设置或使用默认值`)
        return false
      }
    }

    // 检查是否为占位符
    if (typeof value === 'string' && value.includes('placeholder')) {
      if (isDev) {
        console.warn(`百度ASR配置警告: ${field} 使用占位符，请配置真实API密钥`)
        continue
      } else {
        console.error(`百度ASR配置错误: ${field} 不能使用占位符`)
        return false
      }
    }
  }

  return true
}

/**
 * 配置说明和帮助信息
 */
export const CONFIG_HELP = {
  title: '百度ASR配置说明',
  steps: [
    '1. 访问 https://ai.baidu.com 注册百度AI开放平台账号',
    '2. 创建应用并开通"语音技术"中的"短语音识别极速版"服务',
    '3. 获取AppID、API Key、Secret Key',
    '4. 在项目根目录创建 .env.local 文件',
    '5. 添加以下环境变量：',
    '   VITE_BAIDU_APP_ID=你的AppID',
    '   VITE_BAIDU_API_KEY=你的API_Key', 
    '   VITE_BAIDU_SECRET_KEY=你的Secret_Key',
    '6. 重启开发服务器'
  ],
  notes: [
    '• 请妥善保管您的API密钥，不要提交到代码仓库',
    '• 百度ASR极速版支持60秒以内的音频识别',
    '• 推荐使用PCM格式，采样率16000Hz',
    '• 确保在HTTPS环境下使用（本地开发除外）'
  ]
}

/**
 * 打印配置帮助信息
 */
export function printConfigHelp(): void {
  console.group(CONFIG_HELP.title)
  
  console.log('配置步骤:')
  CONFIG_HELP.steps.forEach(step => console.log(step))
  
  console.log('\n注意事项:')
  CONFIG_HELP.notes.forEach(note => console.log(note))
  
  console.groupEnd()
}

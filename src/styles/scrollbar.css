/* 全局滚动条样式 - 优雅的滚动体验 */

/* 隐藏body和html的滚动条 */
html, body {
  overflow: hidden !important;
}

/* Webkit 浏览器 (Chrome, Safari, Edge) - 只针对非body元素 */
*:not(html):not(body) {
  scrollbar-width: thin;
  scrollbar-color: rgba(96, 102, 102, 0.25) rgba(235, 238, 245, 0.15);
}

*:not(html):not(body)::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*:not(html):not(body)::-webkit-scrollbar-track {
  background: rgba(235, 238, 245, 0.15);
  border-radius: 3px;
}

*:not(html):not(body)::-webkit-scrollbar-thumb {
  background: rgba(96, 102, 102, 0.25);
  border-radius: 3px;
  transition: all 0.2s ease;
}

*:not(html):not(body)::-webkit-scrollbar-thumb:hover {
  background: rgba(96, 102, 102, 0.4);
}

*:not(html):not(body)::-webkit-scrollbar-thumb:active {
  background: rgba(96, 102, 102, 0.6);
}

*:not(html):not(body)::-webkit-scrollbar-corner {
  background: rgba(235, 238, 245, 0.15);
} 
/* 移动端响应式设计系统 */

/* 断点定义 - 移动端优先 */
:root {
  /* 断点变量 */
  --breakpoint-xs: 320px;   /* 小屏手机 */
  --breakpoint-sm: 375px;   /* 中屏手机 */
  --breakpoint-md: 768px;   /* 平板 */
  --breakpoint-lg: 1024px;  /* 小桌面 */
  --breakpoint-xl: 1200px;  /* 大桌面 */
  --breakpoint-xxl: 1440px; /* 超大桌面 */
  
  /* 移动端专用变量 */
  --mobile-header-height: 44px;
  --mobile-footer-height: 50px;
  --mobile-padding: 16px;
  --mobile-margin: 12px;
  --mobile-border-radius: 8px;
  --mobile-touch-target: 44px; /* 最小触摸目标尺寸 */
  
  /* 安全区域变量 */
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
  
  /* 移动端字体大小 */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 28px;
  
  /* 移动端行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* 移动端间距系统 */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  
  /* 移动端阴影 */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  
  /* 移动端动画 */
  --transition-fast: 0.15s ease;
  --transition-base: 0.2s ease;
  --transition-slow: 0.3s ease;
}

/* 响应式断点 Mixins */
@mixin mobile-xs {
  @media (min-width: 320px) {
    @content;
  }
}

@mixin mobile-sm {
  @media (min-width: 375px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 768px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1024px) {
    @content;
  }
}

@mixin desktop-lg {
  @media (min-width: 1200px) {
    @content;
  }
}

@mixin desktop-xl {
  @media (min-width: 1440px) {
    @content;
  }
}

/* 移动端基础样式重置 */
.mobile-container {
  width: 100%;
  min-height: 100vh;
  padding-left: var(--safe-area-left);
  padding-right: var(--safe-area-right);
  overflow-x: hidden;
}

.mobile-header {
  height: var(--mobile-header-height);
  padding-top: var(--safe-area-top);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--background-white);
  border-bottom: 1px solid var(--border-lighter);
}

.mobile-content {
  padding-top: calc(var(--mobile-header-height) + var(--safe-area-top));
  padding-bottom: calc(var(--mobile-footer-height) + var(--safe-area-bottom));
  padding-left: var(--mobile-padding);
  padding-right: var(--mobile-padding);
  min-height: 100vh;
  box-sizing: border-box;
}

.mobile-footer {
  height: var(--mobile-footer-height);
  padding-bottom: var(--safe-area-bottom);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: var(--background-white);
  border-top: 1px solid var(--border-lighter);
}

/* 触摸反馈 */
.touch-feedback {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.1);
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
  }
  
  &.touch-active::after {
    width: 200px;
    height: 200px;
  }
}

/* 移动端按钮样式 */
.mobile-btn {
  min-height: var(--mobile-touch-target);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--mobile-border-radius);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: var(--line-height-normal);
  border: none;
  cursor: pointer;
  transition: var(--transition-base);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

/* 移动端输入框样式 */
.mobile-input {
  width: 100%;
  min-height: var(--mobile-touch-target);
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-base);
  border-radius: var(--mobile-border-radius);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  background: var(--background-white);
  transition: var(--transition-base);
  -webkit-appearance: none;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

/* 移动端卡片样式 */
.mobile-card {
  background: var(--background-white);
  border-radius: var(--mobile-border-radius);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  margin-bottom: var(--mobile-margin);
}

/* 移动端列表样式 */
.mobile-list {
  background: var(--background-white);
  border-radius: var(--mobile-border-radius);
  overflow: hidden;
  
  .mobile-list-item {
    padding: var(--space-4);
    border-bottom: 1px solid var(--border-lighter);
    min-height: var(--mobile-touch-target);
    display: flex;
    align-items: center;
    transition: var(--transition-fast);
    
    &:last-child {
      border-bottom: none;
    }
    
    &:active {
      background: var(--background-base);
    }
  }
}

/* 移动端网格系统 */
.mobile-grid {
  display: grid;
  gap: var(--mobile-margin);
  
  &.grid-1 { grid-template-columns: 1fr; }
  &.grid-2 { grid-template-columns: repeat(2, 1fr); }
  &.grid-3 { grid-template-columns: repeat(3, 1fr); }
  &.grid-4 { grid-template-columns: repeat(4, 1fr); }
}

/* 移动端弹性布局 */
.mobile-flex {
  display: flex;
  
  &.flex-col { flex-direction: column; }
  &.flex-wrap { flex-wrap: wrap; }
  &.items-center { align-items: center; }
  &.items-start { align-items: flex-start; }
  &.items-end { align-items: flex-end; }
  &.justify-center { justify-content: center; }
  &.justify-between { justify-content: space-between; }
  &.justify-around { justify-content: space-around; }
  &.justify-evenly { justify-content: space-evenly; }
}

/* 移动端文本样式 */
.mobile-text {
  &.text-xs { font-size: var(--font-size-xs); }
  &.text-sm { font-size: var(--font-size-sm); }
  &.text-base { font-size: var(--font-size-base); }
  &.text-lg { font-size: var(--font-size-lg); }
  &.text-xl { font-size: var(--font-size-xl); }
  &.text-2xl { font-size: var(--font-size-2xl); }
  &.text-3xl { font-size: var(--font-size-3xl); }
  &.text-4xl { font-size: var(--font-size-4xl); }
  
  &.line-tight { line-height: var(--line-height-tight); }
  &.line-normal { line-height: var(--line-height-normal); }
  &.line-relaxed { line-height: var(--line-height-relaxed); }
}

/* 移动端间距工具类 */
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

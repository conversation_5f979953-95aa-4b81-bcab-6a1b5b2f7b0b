<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { showToast } from 'vant'
import { io, Socket } from 'socket.io-client'
import { initLivingRoomApi } from '@/common/request/api/livingRoom'   


import { DEFAULT_BRTC_CONFIG, getBRTCConfigFromEnv  } from '@/config/brtc.config'

// 声明全局BRTC类型
declare global {
  interface Window {
    BRTC_Start: (config: any) => void
    BRTC_Stop: () => void
    BRTC_Version: () => string
    BRTC_GetVideoDevices: (callback: any) => void
    BRTC_GetRemoteUsers: () => any[]
    BRTC_StartPublish: () => void
    BRTC_StopPublish: () => void
    BRTC_MuteMicphone: (muted: boolean) => void
    BRTC_MuteCamera: (muted: boolean) => void
    BRTC_SwitchScreen: () => void
    BRTC_SendMessageToUser: (msg: string, id?: number) => void
    BRTC_SetUserAttribute: (attribute: string) => void
    BRTC_GetUserAttribute: (config: any) => void
    [key: string]: any
  }
}

// BRTC SDK 相关状态
const brtcInitialized = ref(false)
const brtcError = ref('')
const sdkVersion = ref('')

// Socket 连接相关状态
const socket = ref<Socket | null>(null)
const socketConnected = ref(false)
const socketError = ref('')



// 实时语音识别相关状态
const realtimeASR = ref({
  ws: null as WebSocket | null,
  isConnected: false,
  isRecording: false,
  audioStream: null as MediaStream | null,
  audioContext: null as AudioContext | null,
  audioProcessor: null as ScriptProcessorNode | null,
  audioSource: null as MediaStreamAudioSourceNode | null,
  clientId: '',
  sessionId: '',
  currentText: '', // 当前识别的文字（用于字幕显示）
  finalText: '', // 最终确认的文字
  silenceTimer: null as NodeJS.Timeout | null,
  isInitialized: false
})


// 获取BRTC配置（优先使用环境变量，然后使用默认配置）
const globalBrtcConfig = { ...DEFAULT_BRTC_CONFIG, ...getBRTCConfigFromEnv() }

// 生成UUID
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 初始化实时语音识别客户端ID
realtimeASR.value.clientId = generateUUID()

// Socket 连接管理
const initSocket = () => {
  try {
    // 建立安全的WebSocket连接到指定的服务器和命名空间
    socket.value = io('ws://114.111.24.158:6543', {
      path: '/socket/startLivingRoom',
      transports: ['websocket', 'polling'],
      timeout: 10000,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      forceNew: true
    })

    // 连接成功事件
    socket.value.on('connect', () => {
      socketConnected.value = true
      socketError.value = ''
      showToast('Socket连接成功')
    })

    // 连接失败事件
    socket.value.on('connect_error', (error: Error) => {
      socketConnected.value = false
      socketError.value = error.message || '连接失败'
      showToast(`Socket连接失败: ${error.message}`)
    })

    // 断开连接事件
    socket.value.on('disconnect', (reason: string) => {
      socketConnected.value = false
      console.log('🔌 Socket连接断开:', reason)
      showToast('Socket连接断开')
    })

    // 监听其他Socket事件（如果需要）
    socket.value.on('response', (data: string) => {
      console.log('📨 收到服务器响应:', data)
    })

  } catch (error: any) {
    socketError.value = error.message || 'Socket初始化失败'
    showToast(`Socket初始化失败: ${error.message}`)
  }
}

// 发送用户输入
const sendUserInput = (audioText: string) => {
  if (!socket.value || !socketConnected.value) {
    console.warn('⚠️ Socket未连接，无法发送消息')
    showToast('Socket未连接，无法发送消息')
    return
  }

  const userInputData = {
    user_name: 'cmhk',
    audio_text: audioText
  }

  try {
    socket.value.emit('user_input', userInputData)
    console.log('📤 发送用户输入:', userInputData)
    // showToast(`已发送: ${audioText}`)
  } catch (error: any) {
    showToast(`发送失败: ${error.message}`)
  }
}



// 实时语音识别 - 连接WebSocket
const connectRealtimeASR = () => {
  if (realtimeASR.value.ws) {
    realtimeASR.value.ws.close()
  }

  const wsUrl = `ws://106.13.248.214:5345/ws/${realtimeASR.value.clientId}`

  realtimeASR.value.ws = new WebSocket(wsUrl)

  realtimeASR.value.ws.onopen = () => {
    realtimeASR.value.isConnected = true
    showToast('语音识别服务已连接')
  }

  realtimeASR.value.ws.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data)
      handleRealtimeASRMessage(message)
    } catch (error) {
      console.error('解析WebSocket消息失败:', error, event.data)
    }
  }

  realtimeASR.value.ws.onclose = () => {
    realtimeASR.value.isConnected = false
    realtimeASR.value.isInitialized = false
    stopRealtimeRecording()

    // 清理WebSocket引用
    realtimeASR.value.ws = null
  }

  realtimeASR.value.ws.onerror = (error) => {
    console.log(error)
    showToast('语音识别连接错误')
  }
}

// 处理实时语音识别消息
const handleRealtimeASRMessage = (message: any) => {
  const type = message.type

  switch(type) {
    case 'initialized':
      realtimeASR.value.isInitialized = true
      // 自动开始识别
      startRealtimeRecognition()
      break

    case 'recognition_started':
      console.log('🎤 识别会话已开始:', message.session_id)
      realtimeASR.value.sessionId = message.session_id
      break

    case 'recognition_finished':
      // 自动重新开始识别（仅在连接状态正常且页面未卸载时）
      setTimeout(() => {
        if (realtimeASR.value.isConnected &&
            realtimeASR.value.isInitialized &&
            realtimeASR.value.ws &&
            realtimeASR.value.ws.readyState === WebSocket.OPEN) {
          startRealtimeRecognition()
        }
      }, 1000)
      break

    case 'asr_result':
      handleRealtimeASRResult(message.data)
      break

    case 'asr_error':
      console.error('❌ ASR错误:', message.data)
      break

    case 'error':
      console.error('❌ 错误:', message.message)
      break
  }
}

// 处理实时语音识别结果
const handleRealtimeASRResult = (result: any) => {
  const resultType = result.result_type
  const text = result.text

  if (text && text.trim()) {
    if (resultType === 'FIN_TEXT') {
      // 最终结果
      realtimeASR.value.finalText = text.trim()
      realtimeASR.value.currentText = ''

      // 清除静音计时器
      if (realtimeASR.value.silenceTimer) {
        clearTimeout(realtimeASR.value.silenceTimer)
        realtimeASR.value.silenceTimer = null
      }

      // 发送识别结果到Socket
      sendUserInput(text.trim())

    } else {
      // 临时结果，用于字幕显示
      realtimeASR.value.currentText = text.trim()

      // 重置静音计时器
      if (realtimeASR.value.silenceTimer) {
        clearTimeout(realtimeASR.value.silenceTimer)
      }

      // 设置2秒静音检测
      realtimeASR.value.silenceTimer = setTimeout(() => {
        if (realtimeASR.value.currentText) {
          // 2秒无新的语音输入，发送当前文字
          sendUserInput(realtimeASR.value.currentText)
          realtimeASR.value.currentText = ''
        }
      }, 2000)
    }
  }
}

// 开始实时语音识别
const startRealtimeRecognition = async () => {
  if (!realtimeASR.value.ws || realtimeASR.value.ws.readyState !== WebSocket.OPEN) {
    console.warn('⚠️ WebSocket未连接')
    return
  }

  try {
    // 获取麦克风权限
    realtimeASR.value.audioStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: 16000,
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      }
    })

    // 发送开始识别命令
    realtimeASR.value.ws.send(JSON.stringify({
      type: 'start_recognition'
    }))

    // 开始录音
    startRealtimeRecording()

  } catch (error) {
    showToast('无法访问麦克风')
  }
}

// 开始实时录音
const startRealtimeRecording = () => {
  if (!realtimeASR.value.audioStream) return

  // 使用AudioContext进行实时音频处理
  realtimeASR.value.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
  realtimeASR.value.audioSource = realtimeASR.value.audioContext.createMediaStreamSource(realtimeASR.value.audioStream)

  // 使用更小的缓冲区以获得更低的延迟
  realtimeASR.value.audioProcessor = realtimeASR.value.audioContext.createScriptProcessor(2048, 1, 1)

  // 获取实际采样率
  const actualSampleRate = realtimeASR.value.audioContext.sampleRate

  // 音频缓冲区，用于累积160ms的音频数据
  let audioBuffer: number[] = []
  const targetSampleCount = Math.floor(16000 * 0.16) // 160ms = 2560 samples

  // 重采样相关变量
  const resampleRatio = 16000 / actualSampleRate
  let resampleBuffer: number[] = []
  let resampleIndex = 0

  realtimeASR.value.audioProcessor.onaudioprocess = (event) => {
    if (realtimeASR.value.ws && realtimeASR.value.ws.readyState === WebSocket.OPEN) {
      const inputBuffer = event.inputBuffer
      const inputData = inputBuffer.getChannelData(0)

      // 重采样到16kHz（如果需要）
      let processedData: Float32Array | number[]
      if (actualSampleRate === 16000) {
        // 不需要重采样
        processedData = inputData
      } else {
        // 简单的线性重采样
        processedData = []
        for (let i = 0; i < inputData.length; i++) {
          resampleBuffer.push(inputData[i])
        }

        // 重采样
        while (resampleIndex < resampleBuffer.length - 1) {
          const sample = resampleBuffer[Math.floor(resampleIndex)]
          processedData.push(sample)
          resampleIndex += 1 / resampleRatio
        }

        // 清理已处理的数据
        const processedCount = Math.floor(resampleIndex)
        resampleBuffer = resampleBuffer.slice(processedCount)
        resampleIndex -= processedCount
      }

      // 将Float32Array转换为16位PCM并添加到缓冲区
      for (let i = 0; i < processedData.length; i++) {
        // 将-1到1的浮点数转换为-32768到32767的整数
        const sample = Math.max(-32768, Math.min(32767, processedData[i] * 32767))
        audioBuffer.push(sample)
      }

      // 当缓冲区达到160ms的数据量时发送
      if (audioBuffer.length >= targetSampleCount) {
        // 取出160ms的数据
        const chunkData = audioBuffer.splice(0, targetSampleCount)
        const pcmData = new Int16Array(chunkData)

        // 发送PCM数据
        realtimeASR.value.ws!.send(pcmData.buffer)
      }
    }
  }

  realtimeASR.value.audioSource.connect(realtimeASR.value.audioProcessor)
  realtimeASR.value.audioProcessor.connect(realtimeASR.value.audioContext.destination)

  realtimeASR.value.isRecording = true
}

// 停止实时录音
const stopRealtimeRecording = () => {
  // 清理音频处理器
  if (realtimeASR.value.audioProcessor) {
    realtimeASR.value.audioProcessor.disconnect()
    realtimeASR.value.audioProcessor = null
  }

  // 清理音频源
  if (realtimeASR.value.audioSource) {
    realtimeASR.value.audioSource.disconnect()
    realtimeASR.value.audioSource = null
  }

  // 关闭音频上下文
  if (realtimeASR.value.audioContext) {
    realtimeASR.value.audioContext.close()
    realtimeASR.value.audioContext = null
  }

  // 停止音频流
  if (realtimeASR.value.audioStream) {
    realtimeASR.value.audioStream.getTracks().forEach(track => track.stop())
    realtimeASR.value.audioStream = null
  }

  // 清除静音计时器
  if (realtimeASR.value.silenceTimer) {
    clearTimeout(realtimeASR.value.silenceTimer)
    realtimeASR.value.silenceTimer = null
  }

  realtimeASR.value.isRecording = false
  realtimeASR.value.currentText = ''
}

// 断开实时语音识别连接
const disconnectRealtimeASR = () => {
  stopRealtimeRecording()

  if (realtimeASR.value.ws && realtimeASR.value.ws.readyState === WebSocket.OPEN) {
    try {
      // 发送结束识别命令
      realtimeASR.value.ws.send(JSON.stringify({
        type: 'finish_recognition'
      }))

      // 关闭WebSocket连接
      realtimeASR.value.ws.close(1000, 'Manual disconnect')
    } catch (error) {
      // 即使发送失败也要关闭连接
      realtimeASR.value.ws.close()
    }
    realtimeASR.value.ws = null
  }

  realtimeASR.value.isConnected = false
  realtimeASR.value.isInitialized = false
  realtimeASR.value.sessionId = ''
}

// 断开Socket连接
const disconnectSocket = () => {
  if (socket.value) {
    socket.value.disconnect()
    socket.value = null
    socketConnected.value = false
  }
}

/**
 * BRTC_Start() - 启动BRTC SDK
 * 根据百度RTC官方文档实现初始化功能
 * 使用从API获取的配置信息
 */
const BRTC_Start = async () => {
  try {
    // 检查SDK是否可用
    if (!window.BRTC_Start) {
      throw new Error('BRTC SDK未加载，请检查SDK引入')
    }

    // 检查是否有API配置
    if (!apiConfig.value) {
      throw new Error('请先初始化直播间获取配置信息')
    }

    // 使用从API获取的配置信息
    const finalConfig = {
      server: apiConfig.value.url || globalBrtcConfig.server,                    // wss://rtc2.exp.bcelive.com:8989/janus
      appid: apiConfig.value.appId || globalBrtcConfig.appid,                   // apppc7fpsryj72g
      token: apiConfig.value.userToken || globalBrtcConfig.token,               // 用户Token
      roomname: apiConfig.value.roomName || globalBrtcConfig.roomname,          // 房间名称
      userid: apiConfig.value.userId || globalBrtcConfig.userid,                // 用户ID
      displayname: 'cmhk',                                                      // 显示名称
    }

    // 如果API提供了feedId，添加到配置中
    if (apiConfig.value.feedId) {
      (finalConfig as any).feedid = apiConfig.value.feedId
    }

    // 验证必要参数
    if (!finalConfig.appid) {
      throw new Error('AppID不能为空，请先配置AppID')
    }

    if (!finalConfig.token) {
      throw new Error('Token不能为空，请先配置Token')
    }

    // 检查所有必要的BRTC函数是否存在
    const requiredFunctions = ['BRTC_Start', 'BRTC_Version', 'BRTC_Stop']
    const missingFunctions = requiredFunctions.filter(fn => typeof window[fn] !== 'function')

    if (missingFunctions.length > 0) {
      throw new Error(`缺少BRTC函数: ${missingFunctions.join(', ')}`)
    }

    // 根据百度RTC SDK文档，BRTC_Start接受一个配置对象
    const brtcConfig = {
      server: finalConfig.server,                    // WebSocket服务器地址
      appid: finalConfig.appid,                      // 应用ID
      token: finalConfig.token,                      // 用户Token
      roomname: finalConfig.roomname,                // 房间名称
      userid: finalConfig.userid,                    // 用户ID
      displayname: finalConfig.displayname || 'cmhk', // 显示名称
      remotevideoviewid: 'remote-video-container',   // 显示远端视频的DOM元素ID
      localvideoviewid: 'local-video-container',     // 显示本地视频的DOM元素ID
      usingvideo: true,                              // 使用视频
      usingaudio: true,                              // 使用音频
      usingdatachannel: true,                        // 使用数据通道
      aspublisher: true,                             // 作为发布者
      autopublish: true,                             // 自动发布
      autosubscribe: true,                           // 自动订阅
      videoprofile: 'hires',                         // 视频质量配置
      debuglevel: false,                             // 关闭调试日志
      success: () => {
        brtcInitialized.value = true
        brtcError.value = ''

        // 获取SDK版本
        if (window.BRTC_Version) {
          sdkVersion.value = window.BRTC_Version()
        }

        showToast('BRTC SDK初始化成功')
      },
      error: (error: any) => {
        const errorMsg = error?.message || error || '初始化失败'
        brtcError.value = errorMsg
        brtcInitialized.value = false
        showToast(`初始化失败: ${errorMsg}`)
      },
      destroyed: (error: any) => {
        console.log(error)
        brtcInitialized.value = false
        brtcError.value = '连接已断开'
      }
    }


    // 确保在调用前所有变量都已定义
    if (!brtcConfig.server || !brtcConfig.appid || !brtcConfig.token) {
      throw new Error('配置参数不完整')
    }

    window.BRTC_Start(brtcConfig)

  } catch (error: any) {
    const errorMsg = error.message || error.toString() || '初始化失败'
    brtcError.value = errorMsg
    brtcInitialized.value = false


    // 检查是否是变量引用错误
    if (error.name === 'ReferenceError') {
      console.error('🔍 这是一个变量引用错误，可能是变量提升问题')
    }

    showToast(`初始化失败: ${errorMsg}`)

    throw error
  }
}

// 检查SDK加载状态
const checkSDKLoaded = () => {
  return new Promise<boolean>((resolve) => {
    let attempts = 0
    const maxAttempts = 50 // 最多等待5秒

    const checkInterval = setInterval(() => {
      attempts++

      if (window.BRTC_Version) {
        clearInterval(checkInterval)
        sdkVersion.value = window.BRTC_Version() || 'Unknown'

        resolve(true)
      } else if (attempts >= maxAttempts) {
        clearInterval(checkInterval)
        resolve(false)
      }
    }, 100)
  })
}

// 初始化BRTC SDK
const initBRTC = async () => {
  try {
    // 等待SDK加载
    const sdkLoaded = await checkSDKLoaded()

    if (sdkLoaded) {
      // 先初始化直播间获取配置，然后初始化BRTC
      try {
        // await initLivingRoom()
        await BRTC_Start()
      } catch (error) {
        console.error('初始化失败:', error)
      }
    } else {
      brtcError.value = 'BRTC SDK加载失败'
      showToast('BRTC SDK加载失败')
    }
  } catch (error: any) {
    console.error('BRTC初始化失败:', error)
  }
}



// 存储从接口获取的BRTC配置
const apiConfig = ref<any>(null)



// 初始化直播间
const initLivingRoom = async () => {
  try {
    const result = await initLivingRoomApi('cmhk')
    if (result.code === '000000' && result.data && result.data.brtc_data) {
      // 保存API返回的BRTC配置信息
      apiConfig.value = result.data.brtc_data
    } else {
      throw new Error('API返回数据格式不正确')
    }

    showToast('直播间初始化成功')

    return result
  } catch (error: any) {
    const errorMsg = error.message || '直播间初始化失败'
    showToast(`直播间初始化失败: ${errorMsg}`)
    throw error
  }
}

// 综合初始化方法
const init = async () => {
  try {
    // 初始化Socket连接
    try {
      initSocket()
    } catch (error) {
      console.error('❌ Socket初始化失败:', error)
      showToast('Socket初始化失败')
    }

    // 延迟初始化实时语音识别，确保Socket连接稳定后再启动
    setTimeout(() => {
      try {
        connectRealtimeASR()
      } catch (error) {
        console.error('❌ 实时语音识别初始化失败:', error)
        showToast('语音识别初始化失败')
      }
    }, 2000)

    // 先初始化直播间获取配置，再初始化BRTC SDK
    try {
      await initLivingRoom()
    } catch (error) {
      console.error('❌ 直播间初始化失败:', error)
      showToast('直播间初始化失败，将使用默认配置')
    }

    // 然后初始化BRTC SDK
    try {
      await initBRTC()
    } catch (error) {
      console.error('❌ BRTC SDK初始化失败:', error)
    }

  } catch (error: any) {
    console.error('❌ 应用初始化失败:', error)
    showToast('应用初始化失败')
  }
}

// 生命周期
onMounted(async () => {
  await init()

  // 添加页面卸载监听器
  window.addEventListener('beforeunload', handleBeforeUnload)
})

// 页面卸载前的清理处理
const handleBeforeUnload = () => {
  console.log('🔄 页面即将卸载，清理资源...')

  // 向ASR后台发送结束连接指令
  if (realtimeASR.value.ws && realtimeASR.value.ws.readyState === WebSocket.OPEN) {
    try {
      // 发送结束识别命令
      realtimeASR.value.ws.send(JSON.stringify({
        type: 'finish_recognition'
      }))
      console.log('📤 页面卸载：已发送结束识别指令')

      // 关闭WebSocket连接
      realtimeASR.value.ws.close(1000, 'Page unloading')
    } catch (error) {
      console.error('❌ 页面卸载：发送结束指令失败:', error)
    }
  }

  // 停止录音
  stopRealtimeRecording()
}

onUnmounted(() => {
  // 执行页面卸载前的清理
  handleBeforeUnload()

  // 清理实时语音识别连接
  try {
    disconnectRealtimeASR()
  } catch (error) {
    console.error('❌ 断开实时语音识别连接失败:', error)
  }

  // 清理Socket连接
  try {
    disconnectSocket()
  } catch (error) {
    console.error('❌ 断开Socket连接失败:', error)
  }

  // 清理BRTC连接
  if (brtcInitialized.value && window.BRTC_Stop) {
    try {
      window.BRTC_Stop()
      console.log('🛑 BRTC SDK已停止')
    } catch (error) {
      console.error('❌ 停止BRTC SDK失败:', error)
    }
  }

  // 移除页面卸载监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<template>
  <div class="voice-demo-view">
    <!-- BRTC 视频容器作为全屏背景 -->
    <div
      id="remote-video-container"
      class="video-background"
      v-show="brtcInitialized"
    >
      <!-- 这个容器将被BRTC SDK用来显示远端视频 -->
    </div>

    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-item">
        <van-icon
          :name="brtcInitialized ? 'success' : (brtcError ? 'cross' : 'loading')"
          :color="brtcInitialized ? '#07c160' : (brtcError ? '#ee0a24' : '#ff976a')"
        />
        <span class="status-text">
          {{ brtcInitialized ? 'BRTC已连接' : (brtcError ? 'BRTC连接失败' : 'BRTC连接中...') }}
        </span>
      </div>

      <div class="status-item">
        <van-icon
          :name="socketConnected ? 'success' : (socketError ? 'cross' : 'loading')"
          :color="socketConnected ? '#07c160' : (socketError ? '#ee0a24' : '#ff976a')"
        />
        <span class="status-text">
          {{ socketConnected ? 'Socket已连接' : (socketError ? 'Socket连接失败' : 'Socket连接中...') }}
        </span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.voice-demo-view {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

/* 视频背景 */
.video-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* BRTC 远端视频容器样式 */
.remote-video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  pointer-events: none; /* 允许点击穿透到下层元素 */
}

/* 当有远端视频时的样式 */
.remote-video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: auto;
}

/* BRTC 本地视频容器样式 */
.local-video-container {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 120px;
  height: 160px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  overflow: hidden;
  z-index: 10;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.local-video-container video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 顶部状态栏 */
.status-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background: linear-gradient(180deg, rgba(0,0,0,0.8) 0%, transparent 100%);
  z-index: 10;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.status-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}







/* 实时语音识别字幕 */
.subtitle-display {
  position: absolute;
  bottom: 120px;
  left: 50%;
  transform: translateX(-50%);
  max-width: 90%;
  z-index: 15;
  text-align: center;
}

.subtitle-text {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 16px;
  line-height: 1.4;
  margin-bottom: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 20px;
  word-break: break-all;
}

.subtitle-status {
  font-size: 12px;
  opacity: 0.8;
}

.recording-indicator {
  color: #ff4444;
  animation: pulse 1s infinite;
}

.connected-indicator {
  color: #00ff00;
}

.disconnected-indicator {
  color: #ff4444;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}



.tips {
  text-align: center;
}

.tip-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

/* 配置警告 */
.config-warning {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 20;
}

/* 错误提示 */
.error-notice {
  position: absolute;
  top: 80px;
  left: 20px;
  right: 20px;
  z-index: 15;
}

/* 移动端适配 */
@media (max-width: 767px) {
  .status-bar {
    padding: 15px;
  }
  
  .status-item {
    padding: 6px 10px;
  }
  
  .status-text {
    font-size: 11px;
  }
  

  
  .result-text {
    font-size: 14px;
  }
  

  
  .tip-text {
    font-size: 11px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .status-bar {
    padding: 10px 20px;
  }
  

  

}
</style>

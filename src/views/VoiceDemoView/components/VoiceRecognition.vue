<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue'
import {
  createSpeechRecognition,
  checkSpeechRecognitionAvailability,
  type SpeechRecognitionManager,
  type SpeechRecognitionResult
} from '@/utils/speechRecognition'

// Props定义
interface Props {
  recording?: boolean
  continuous?: boolean
  interimResults?: boolean
  lang?: string
  maxAlternatives?: number
}

const props = withDefaults(defineProps<Props>(), {
  recording: false,
  continuous: true,
  interimResults: true,
  lang: 'zh-CN',
  maxAlternatives: 1
})

// Emits定义
const emit = defineEmits<{
  start: []
  end: []
  result: [text: string]
  error: [error: string]
  noMatch: []
}>()

// 响应式数据
const isSupported = ref(false)
const isRecording = ref(false)
const isListening = ref(false)
const currentText = ref('')
const finalText = ref('')
const confidence = ref(0)
const speechManager = ref<SpeechRecognitionManager | null>(null)

// 初始化语音识别
const initRecognition = () => {
  // 检查支持性
  const availability = checkSpeechRecognitionAvailability()
  isSupported.value = availability.supported && availability.https

  if (!isSupported.value) {
    console.error('语音识别不可用:', availability.message)
    return false
  }

  try {
    // 创建语音识别管理器
    speechManager.value = createSpeechRecognition(
      {
        continuous: props.continuous,
        interimResults: props.interimResults,
        lang: props.lang,
        maxAlternatives: props.maxAlternatives
      },
      {
        onStart: handleStart,
        onEnd: handleEnd,
        onResult: handleResult,
        onError: handleError,
        onNoMatch: handleNoMatch,
        onSpeechStart: handleSpeechStart,
        onSpeechEnd: handleSpeechEnd
      }
    )

    return speechManager.value !== null
  } catch (error) {
    console.error('语音识别初始化失败:', error)
    isSupported.value = false
    return false
  }
}

// 开始录音
const startRecording = async () => {
  if (!isSupported.value || !speechManager.value) {
    emit('error', '浏览器不支持语音识别功能')
    return
  }

  if (isRecording.value) {
    console.warn('语音识别已在进行中')
    return
  }

  // 重置状态
  currentText.value = ''
  finalText.value = ''
  confidence.value = 0

  // 开始识别
  const success = await speechManager.value.start()
  if (!success) {
    emit('error', '启动语音识别失败，请检查麦克风权限')
  }
}

// 停止录音
const stopRecording = () => {
  if (!speechManager.value || !isRecording.value) {
    return
  }

  speechManager.value.stop()
}

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording()
  } else {
    startRecording()
  }
}



// 事件处理器
const handleStart = () => {
  console.log('语音识别开始')
  isListening.value = true
  emit('start')
}

const handleEnd = () => {
  console.log('语音识别结束')
  isRecording.value = false
  isListening.value = false
  emit('end')
}

const handleResult = (result: SpeechRecognitionResult) => {
  if (result.isFinal) {
    finalText.value = result.transcript
    confidence.value = result.confidence
    emit('result', result.transcript)
    console.log('最终识别结果:', result.transcript, '置信度:', result.confidence)
  } else {
    currentText.value = result.transcript
    console.log('临时识别结果:', result.transcript)
  }
}

const handleError = (errorType: string, message: string) => {
  console.error('语音识别错误:', errorType, message)

  isRecording.value = false
  isListening.value = false

  emit('error', message)
}

const handleNoMatch = () => {
  console.log('没有匹配的识别结果')
  emit('noMatch')
}

const handleSpeechStart = () => {
  console.log('检测到语音输入')
}

const handleSpeechEnd = () => {
  console.log('语音输入结束')
}


const buttonIcon = computed(() => {
  if (!isSupported.value) return 'warning-o'
  if (isRecording.value) return 'pause'
  return 'volume-o'
})

// 生命周期
onMounted(() => {
  initRecognition()
})

onUnmounted(() => {
  if (speechManager.value) {
    speechManager.value.destroy()
  }
})

// 暴露方法给父组件
defineExpose({
  startRecording,
  stopRecording,
  toggleRecording,
  isSupported: isSupported.value,
  isRecording: isRecording.value
})
</script>

<template>
  <div class="voice-recognition">
    <!-- 主要录音按钮 -->
    <div class="record-button-container">
      <div 
        class="record-button"
        :class="{ 
          'recording': isRecording, 
          'listening': isListening,
          'disabled': !isSupported 
        }"
        @click="toggleRecording"
        @touchstart.prevent="startRecording"
        @touchend.prevent="stopRecording"
      >
        <div class="button-inner">
          <van-icon :name="buttonIcon" size="32" />
        </div>
        
        <!-- 录音动画波纹 -->
        <div v-if="isRecording" class="recording-waves">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
        </div>
      </div>
      
      <!-- 按钮文字 -->
      <div class="button-text">
        <span v-if="!isSupported" class="error-text">不支持语音识别</span>
        <span v-else-if="isRecording" class="recording-text">正在录音...</span>
        <span v-else class="normal-text">点击开始录音</span>
      </div>
    </div>

    <!-- 实时识别结果显示 -->
    <div v-if="isRecording && (currentText || finalText)" class="live-result">
      <div class="result-container">
        <!-- 最终结果 -->
        <div v-if="finalText" class="final-text">
          {{ finalText }}
        </div>
        
        <!-- 临时结果 -->
        <div v-if="currentText" class="interim-text">
          {{ currentText }}
        </div>
        
        <!-- 置信度显示 -->
        <div v-if="confidence > 0" class="confidence">
          置信度: {{ Math.round(confidence * 100) }}%
        </div>
      </div>
    </div>

    <!-- 操作提示 -->
    <div class="operation-tips">
      <div class="tip-item">
        <van-icon name="info-o" size="14" />
        <span>点击按钮开始/停止录音</span>
      </div>
      <div class="tip-item">
        <van-icon name="info-o" size="14" />
        <span>长按按钮持续录音</span>
      </div>
    </div>


  </div>
</template>

<script lang="ts">
// 全局类型声明
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}

export default {
  name: 'VoiceRecognition'
}
</script>

<style scoped>
.voice-recognition {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

/* 录音按钮容器 */
.record-button-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* 主录音按钮 */
.record-button {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1989fa 0%, #1976d2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
}

.record-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(25, 137, 250, 0.4);
}

.record-button:active {
  transform: scale(0.95);
}

.record-button.recording {
  background: linear-gradient(135deg, #ee0a24 0%, #d32f2f 100%);
  box-shadow: 0 4px 12px rgba(238, 10, 36, 0.3);
  animation: pulse 1.5s infinite;
}

.record-button.disabled {
  background: #c8c9cc;
  cursor: not-allowed;
  box-shadow: none;
}

.record-button.disabled:hover {
  transform: none;
}

.button-inner {
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 录音动画波纹 */
.recording-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(238, 10, 36, 0.6);
  border-radius: 50%;
  animation: wave-animation 2s infinite;
}

.wave-1 {
  width: 100px;
  height: 100px;
  animation-delay: 0s;
}

.wave-2 {
  width: 120px;
  height: 120px;
  animation-delay: 0.5s;
}

.wave-3 {
  width: 140px;
  height: 140px;
  animation-delay: 1s;
}

@keyframes wave-animation {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 按钮文字 */
.button-text {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.normal-text {
  color: #323233;
}

.recording-text {
  color: #ee0a24;
  animation: blink 1s infinite;
}

.error-text {
  color: #c8c9cc;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 实时结果显示 */
.live-result {
  width: 100%;
  max-width: 300px;
  margin-top: 16px;
}

.result-container {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 3px solid #1989fa;
}

.final-text {
  font-size: 15px;
  color: #323233;
  line-height: 1.5;
  margin-bottom: 8px;
  font-weight: 500;
}

.interim-text {
  font-size: 14px;
  color: #969799;
  line-height: 1.4;
  font-style: italic;
  margin-bottom: 8px;
}

.confidence {
  font-size: 12px;
  color: #1989fa;
  text-align: right;
}

/* 操作提示 */
.operation-tips {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #969799;
}

/* 移动端优化 */
@media (max-width: 767px) {
  .record-button {
    width: 70px;
    height: 70px;
  }
  
  .button-inner :deep(.van-icon) {
    font-size: 28px;
  }
  
  .wave-1 {
    width: 90px;
    height: 90px;
  }
  
  .wave-2 {
    width: 110px;
    height: 110px;
  }
  
  .wave-3 {
    width: 130px;
    height: 130px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .record-button:hover {
    transform: none;
    box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
  }
  
  .record-button.recording:hover {
    box-shadow: 0 4px 12px rgba(238, 10, 36, 0.3);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .record-button {
    border: 2px solid #000;
  }
  
  .result-container {
    border: 1px solid #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .record-button,
  .wave,
  .recording-text {
    animation: none;
  }
  
  .record-button:hover {
    transform: none;
  }
}
</style>

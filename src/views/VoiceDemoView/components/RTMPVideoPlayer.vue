<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  createDigitalHumanStream,
  type DigitalHumanStreamManager,
  type StreamStatus
} from '@/utils/rtmpPlayer'

// Props定义
interface Props {
  url: string
  loading?: boolean
  error?: boolean
  autoplay?: boolean
  muted?: boolean
  controls?: boolean
  poster?: string
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  error: false,
  autoplay: true,
  muted: true,
  controls: false,
  poster: ''
})

// Emits定义
const emit = defineEmits<{
  load: []
  error: [error: string]
  play: []
  pause: []
  ended: []
}>()

// 响应式数据
const videoRef = ref<HTMLVideoElement>()
const isPlaying = ref(false)
const isLoading = ref(false)
const hasError = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const volume = ref(1)
const isMuted = ref(props.muted)
const streamManager = ref<DigitalHumanStreamManager | null>(null)
const streamStatus = ref<StreamStatus>('idle')


// 初始化视频播放器
const initPlayer = async () => {
  if (!videoRef.value || !props.url) return

  try {
    isLoading.value = true
    hasError.value = false

    // 创建流媒体管理器
    streamManager.value = createDigitalHumanStream(
      {
        url: props.url,
        autoplay: props.autoplay,
        muted: props.muted,
        controls: props.controls,
        poster: props.poster
      },
      {
        onStatusChange: (status) => {
          streamStatus.value = status
          isLoading.value = status === 'loading'
          isPlaying.value = status === 'playing'
          hasError.value = status === 'error'
        },
        onLoad: () => emit('load'),
        onPlay: () => emit('play'),
        onPause: () => emit('pause'),
        onError: (error) => emit('error', error),
        onEnded: () => emit('ended'),
        onTimeUpdate: (time, dur) => {
          currentTime.value = time
          duration.value = dur
        }
      }
    )

    // 初始化视频元素
    streamManager.value.init(videoRef.value)

    // 加载视频
    await streamManager.value.load()

  } catch (error) {
    console.error('视频初始化失败:', error)
    hasError.value = true
    emit('error', '视频初始化失败')
  }
}

// 播放视频
const play = async () => {
  if (streamManager.value) {
    await streamManager.value.play()
  }
}

// 暂停视频
const pause = () => {
  if (streamManager.value) {
    streamManager.value.pause()
  }
}

// 切换播放状态
const togglePlay = () => {
  if (isPlaying.value) {
    pause()
  } else {
    play()
  }
}

// 设置音量
const setVolume = (vol: number) => {
  if (streamManager.value) {
    volume.value = Math.max(0, Math.min(1, vol))
    streamManager.value.setVolume(volume.value)
  }
}

// 切换静音
const toggleMute = () => {
  if (streamManager.value) {
    streamManager.value.toggleMute()
    isMuted.value = !isMuted.value
  }
}

// 重试加载
const retryLoad = () => {
  if (streamManager.value) {
    streamManager.value.load()
  }
}

// 监听URL变化
watch(() => props.url, () => {
  if (props.url) {
    nextTick(() => {
      initPlayer()
    })
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  if (props.url) {
    initPlayer()
  }
})

onUnmounted(() => {
  if (streamManager.value) {
    streamManager.value.destroy()
  }
})

// 暴露方法给父组件
defineExpose({
  play,
  pause,
  togglePlay,
  setVolume,
  toggleMute,
  videoElement: videoRef
})
</script>

<template>
  <div class="rtmp-video-player">
    <!-- 视频元素 -->
    <video
      ref="videoRef"
      class="video-element"
      :class="{ 'video-loading': isLoading, 'video-error': hasError }"
      playsinline
      webkit-playsinline
      x5-playsinline
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
    >
      您的浏览器不支持视频播放
    </video>

    <!-- 加载状态 -->
    <div v-if="loading || isLoading" class="video-overlay loading-overlay">
      <van-loading type="spinner" color="#ffffff" size="24px">
        加载中...
      </van-loading>
    </div>

    <!-- 错误状态 -->
    <div v-if="error || hasError" class="video-overlay error-overlay">
      <div class="error-content">
        <van-icon name="warning-o" size="32" color="#ee0a24" />
        <p>视频加载失败</p>
        <van-button
          type="primary"
          size="small"
          @click="retryLoad"
          class="retry-btn"
        >
          重试
        </van-button>
      </div>
    </div>

    <!-- 自定义控制栏（可选） -->
    <div v-if="!controls && !isLoading && !hasError" class="custom-controls">
      <div class="control-left">
        <van-icon 
          :name="isPlaying ? 'pause' : 'play'" 
          size="20"
          @click="togglePlay"
          class="control-btn"
        />
      </div>
      
      <div class="control-right">
        <van-icon 
          :name="isMuted ? 'volume-o' : 'volume'" 
          size="18"
          @click="toggleMute"
          class="control-btn"
        />
      </div>
    </div>

    <!-- 点击播放遮罩 -->
    <div 
      v-if="!isPlaying && !isLoading && !hasError" 
      class="play-overlay"
      @click="togglePlay"
    >
      <div class="play-button">
        <van-icon name="play" size="48" color="#ffffff" />
      </div>
    </div>
  </div>
</template>

<style scoped>
.rtmp-video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: inherit;
  overflow: hidden;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #000;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

.loading-overlay {
  background: rgba(0, 0, 0, 0.5);
}

.error-overlay {
  background: rgba(0, 0, 0, 0.8);
}

.error-content {
  text-align: center;
}

.error-content p {
  margin: 12px 0;
  font-size: 14px;
  color: #ffffff;
}

.retry-btn {
  margin-top: 8px;
}

.custom-controls {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 48px;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.rtmp-video-player:hover .custom-controls {
  opacity: 1;
}

.control-left,
.control-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.control-btn:active {
  transform: scale(0.95);
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.play-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.play-button:active {
  transform: scale(0.95);
}

/* 移动端优化 */
@media (max-width: 767px) {
  .custom-controls {
    height: 44px;
    padding: 0 12px;
  }
  
  .play-button {
    width: 60px;
    height: 60px;
  }
  
  .control-btn {
    padding: 6px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .custom-controls {
    opacity: 1;
  }
  
  .control-btn:hover {
    background-color: transparent;
  }
  
  .control-btn:active {
    background-color: rgba(255, 255, 255, 0.2);
  }
}

/* 全屏适配 */
.video-element:fullscreen {
  object-fit: contain;
}

.video-element:-webkit-full-screen {
  object-fit: contain;
}

.video-element:-moz-full-screen {
  object-fit: contain;
}

.video-element:-ms-fullscreen {
  object-fit: contain;
}
</style>

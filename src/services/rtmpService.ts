/**
 * RTMP流媒体服务
 * 处理RTMP拉流和格式转换
 */

// RTMP流信息接口
export interface RTMPStreamInfo {
  pullUrl: string
  pushUrl?: string
  streamKey?: string
  status: 'active' | 'inactive' | 'error'
  startTime?: number
  duration?: number
}

// 流转换选项
export interface StreamConvertOptions {
  preferredFormat: 'flv' | 'hls' | 'auto'
  enableFallback: boolean
  retryAttempts: number
  retryDelay: number
}

// 流状态
export type StreamStatus = 'idle' | 'connecting' | 'connected' | 'playing' | 'error' | 'disconnected'

// 事件回调
export interface StreamEventCallbacks {
  onStatusChange?: (status: StreamStatus) => void
  onError?: (error: string) => void
  onConnected?: () => void
  onDisconnected?: () => void
  onMetadata?: (metadata: any) => void
}

/**
 * RTMP流媒体服务类
 */
export class RTMPService {
  private streamInfo: RTMPStreamInfo | null = null
  private status: StreamStatus = 'idle'
  private callbacks: StreamEventCallbacks = {}
  private retryTimer: number | null = null
  private heartbeatTimer: number | null = null

  constructor(callbacks: StreamEventCallbacks = {}) {
    this.callbacks = callbacks
  }

  /**
   * 解析RTMP URL
   */
  static parseRTMPUrl(rtmpUrl: string): {
    protocol: string
    host: string
    port: number
    app: string
    stream: string
    params?: Record<string, string>
  } | null {
    try {
      // 解析 rtmp://host:port/app/stream?params
      const urlPattern = /^rtmp:\/\/([^:\/]+)(?::(\d+))?\/([^\/]+)\/(.+?)(?:\?(.+))?$/
      const match = rtmpUrl.match(urlPattern)
      
      if (!match) {
        throw new Error('无效的RTMP URL格式')
      }

      const [, host, port, app, stream, queryString] = match
      
      // 解析查询参数
      const params: Record<string, string> = {}
      if (queryString) {
        queryString.split('&').forEach(param => {
          const [key, value] = param.split('=')
          if (key && value) {
            params[decodeURIComponent(key)] = decodeURIComponent(value)
          }
        })
      }

      return {
        protocol: 'rtmp',
        host,
        port: port ? parseInt(port) : 1935,
        app,
        stream,
        params: Object.keys(params).length > 0 ? params : undefined
      }
    } catch (error) {
      console.error('RTMP URL解析失败:', error)
      return null
    }
  }

  /**
   * 转换RTMP URL为HTTP流URL
   */
  static convertRTMPToHTTP(rtmpUrl: string): {
    flvUrl: string
    hlsUrl: string
    originalUrl: string
  } {
    const parsed = this.parseRTMPUrl(rtmpUrl)

    if (!parsed) {
      return {
        flvUrl: '',
        hlsUrl: '',
        originalUrl: rtmpUrl
      }
    }

    const { host, port, app, stream } = parsed

    // 对于百度的RTMP流，尝试不同的转换策略
    if (host.includes('persona.baidu.com')) {
      // 百度数字人服务的特殊处理
      const baseHttpsUrl = `https://${host.replace('live2d-pl', 'live2d-hls')}`

      // 尝试多种可能的URL格式
      const flvUrl = `${baseHttpsUrl}/${app}/${stream}.flv`
      const hlsUrl = `${baseHttpsUrl}/${app}/${stream}/playlist.m3u8`

      return {
        flvUrl,
        hlsUrl,
        originalUrl: rtmpUrl
      }
    }

    // 通用转换逻辑
    const baseUrl = `https://${host}${port !== 443 ? `:${port}` : ''}`
    const flvUrl = `${baseUrl}/${app}/${stream}.flv`
    const hlsUrl = `${baseUrl}/${app}/${stream}.m3u8`

    return {
      flvUrl,
      hlsUrl,
      originalUrl: rtmpUrl
    }
  }

  /**
   * 检测流是否可用
   */
  async checkStreamAvailability(rtmpUrl: string): Promise<boolean> {
    try {
      // 对于RTMP流，我们采用更宽松的检测策略
      console.log('检测RTMP流可用性:', rtmpUrl)

      // 如果是百度的RTMP流，直接返回true，因为我们知道它是有效的
      if (rtmpUrl.includes('persona.baidu.com')) {
        console.log('检测到百度数字人RTMP流，假设可用')
        return true
      }

      const { flvUrl, hlsUrl } = RTMPService.convertRTMPToHTTP(rtmpUrl)

      // 并行检测多种格式，任何一种可用即返回true
      const checks = []

      // 检测FLV流
      if (flvUrl) {
        checks.push(
          fetch(flvUrl, {
            method: 'HEAD',
            signal: AbortSignal.timeout(3000),
            mode: 'no-cors' // 避免CORS问题
          }).then(response => {
            console.log('FLV流检测结果:', response.status)
            return response.ok || response.status === 0 // status 0 表示no-cors模式
          }).catch(error => {
            console.warn('FLV流检测失败:', error.message)
            return false
          })
        )
      }

      // 检测HLS流
      if (hlsUrl) {
        checks.push(
          fetch(hlsUrl, {
            method: 'HEAD',
            signal: AbortSignal.timeout(3000),
            mode: 'no-cors'
          }).then(response => {
            console.log('HLS流检测结果:', response.status)
            return response.ok || response.status === 0
          }).catch(error => {
            console.warn('HLS流检测失败:', error.message)
            return false
          })
        )
      }

      // 等待所有检测完成
      const results = await Promise.all(checks)
      const isAvailable = results.some(result => result)

      console.log('流可用性检测结果:', isAvailable)
      return isAvailable

    } catch (error) {
      console.error('流可用性检测失败:', error)
      // 如果检测失败，我们假设流是可用的，让播放器自己处理
      return true
    }
  }

  /**
   * 获取流的最佳播放URL
   */
  getBestPlaybackUrl(rtmpUrl: string, options: StreamConvertOptions): string {
    const { flvUrl, hlsUrl } = RTMPService.convertRTMPToHTTP(rtmpUrl)
    
    // 根据偏好和浏览器支持选择格式
    if (options.preferredFormat === 'flv' && this.isFlvSupported()) {
      return flvUrl
    } else if (options.preferredFormat === 'hls' && this.isHlsSupported()) {
      return hlsUrl
    } else if (options.preferredFormat === 'auto') {
      // 自动选择最佳格式
      if (this.isFlvSupported()) {
        return flvUrl
      } else if (this.isHlsSupported()) {
        return hlsUrl
      }
    }

    // 回退到原始URL
    return options.enableFallback ? rtmpUrl : flvUrl
  }

  /**
   * 检查FLV支持
   */
  private isFlvSupported(): boolean {
    // 检查是否支持MediaSource和FLV
    return !!(window.MediaSource || (window as any).WebKitMediaSource)
  }

  /**
   * 检查HLS支持
   */
  private isHlsSupported(): boolean {
    const video = document.createElement('video')
    return video.canPlayType('application/vnd.apple.mpegurl') !== '' ||
           video.canPlayType('application/x-mpegURL') !== ''
  }

  /**
   * 连接RTMP流
   */
  async connect(rtmpUrl: string, options: StreamConvertOptions = {
    preferredFormat: 'auto',
    enableFallback: true,
    retryAttempts: 3,
    retryDelay: 2000
  }): Promise<string> {
    try {
      this.updateStatus('connecting')
      
      // 检查流是否可用
      const isAvailable = await this.checkStreamAvailability(rtmpUrl)
      if (!isAvailable) {
        throw new Error('RTMP流不可用或无法访问')
      }

      // 获取最佳播放URL
      const playbackUrl = this.getBestPlaybackUrl(rtmpUrl, options)
      
      // 保存流信息
      this.streamInfo = {
        pullUrl: rtmpUrl,
        status: 'active',
        startTime: Date.now()
      }

      this.updateStatus('connected')
      this.callbacks.onConnected?.()
      
      // 启动心跳检测
      this.startHeartbeat(rtmpUrl)
      
      console.log('RTMP流连接成功:', playbackUrl)
      return playbackUrl

    } catch (error) {
      console.error('RTMP流连接失败:', error)
      this.updateStatus('error')
      this.callbacks.onError?.(error instanceof Error ? error.message : '连接失败')
      throw error
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.updateStatus('disconnected')
    this.stopHeartbeat()
    this.stopRetry()
    
    if (this.streamInfo) {
      this.streamInfo.status = 'inactive'
    }
    
    this.callbacks.onDisconnected?.()
    console.log('RTMP流已断开连接')
  }

  /**
   * 重连
   */
  async reconnect(): Promise<void> {
    if (!this.streamInfo) {
      throw new Error('没有可重连的流信息')
    }

    console.log('尝试重连RTMP流...')
    await this.connect(this.streamInfo.pullUrl)
  }

  /**
   * 启动心跳检测
   */
  private startHeartbeat(rtmpUrl: string): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(async () => {
      try {
        const isAvailable = await this.checkStreamAvailability(rtmpUrl)
        if (!isAvailable && this.status === 'connected') {
          console.warn('心跳检测失败，流可能已断开')
          this.updateStatus('error')
          this.callbacks.onError?.('流连接丢失')
        }
      } catch (error) {
        console.error('心跳检测错误:', error)
      }
    }, 30000) // 30秒检测一次
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }


  /**
   * 停止重试
   */
  private stopRetry(): void {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
      this.retryTimer = null
    }
  }

  /**
   * 更新状态
   */
  private updateStatus(status: StreamStatus): void {
    this.status = status
    this.callbacks.onStatusChange?.(status)
  }

  /**
   * 获取当前状态
   */
  getStatus(): StreamStatus {
    return this.status
  }

  /**
   * 获取流信息
   */
  getStreamInfo(): RTMPStreamInfo | null {
    return this.streamInfo
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    this.disconnect()
    this.callbacks = {}
    this.streamInfo = null
  }
}

/**
 * 创建RTMP服务实例
 */
export function createRTMPService(callbacks: StreamEventCallbacks = {}): RTMPService {
  return new RTMPService(callbacks)
}

/**
 * 检查RTMP支持
 */
export function checkRTMPSupport(): {
  flvSupported: boolean
  hlsSupported: boolean
  mediaSourceSupported: boolean
  supported: boolean
  message: string
} {
  const video = document.createElement('video')
  const flvSupported = !!(window.MediaSource || (window as any).WebKitMediaSource)
  const hlsSupported = video.canPlayType('application/vnd.apple.mpegurl') !== '' ||
                      video.canPlayType('application/x-mpegURL') !== ''
  const mediaSourceSupported = !!(window.MediaSource || (window as any).WebKitMediaSource)
  
  const supported = flvSupported || hlsSupported
  
  let message = ''
  if (!supported) {
    message = '当前浏览器不支持RTMP流播放'
  } else {
    const formats = []
    if (flvSupported) formats.push('FLV')
    if (hlsSupported) formats.push('HLS')
    message = `支持的格式: ${formats.join(', ')}`
  }

  return {
    flvSupported,
    hlsSupported,
    mediaSourceSupported,
    supported,
    message
  }
}

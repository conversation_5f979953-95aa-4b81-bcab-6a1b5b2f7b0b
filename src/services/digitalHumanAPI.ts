/**
 * 数字人API服务
 * 处理数字人相关的API调用
 */

// 数字人配置接口
export interface DigitalHumanConfig {
  mode: 'RTMP' | 'WebRTC' | 'HLS'
  pushUrl?: string
  quality?: 'low' | 'medium' | 'high'
  resolution?: '720p' | '1080p'
  bitrate?: number
}

// 数字人响应接口
export interface DigitalHumanResponse {
  success: boolean
  data?: {
    rtmp?: {
      pullUrl: string
      pushUrl?: string
    }
    webrtc?: {
      sdp: string
      iceServers: any[]
    }
    hls?: {
      playlistUrl: string
    }
    sessionId: string
    status: 'active' | 'inactive' | 'error'
  }
  error?: {
    code: string
    message: string
  }
}

// 流状态接口
export interface StreamStatus {
  sessionId: string
  status: 'active' | 'inactive' | 'error'
  startTime?: number
  duration?: number
  viewers?: number
}

/**
 * 数字人API服务类
 */
export class DigitalHumanAPI {
  private baseUrl: string
  private apiKey: string
  private sessionId: string | null = null

  constructor(baseUrl: string = '', apiKey: string = '') {
    this.baseUrl = baseUrl || import.meta.env.VITE_API_BASE_URL || ''
    this.apiKey = apiKey || import.meta.env.VITE_API_KEY || ''
  }

  /**
   * 创建数字人会话
   */
  async createSession(config: DigitalHumanConfig): Promise<DigitalHumanResponse> {
    try {
      // 在开发环境返回模拟数据
      if (import.meta.env.DEV || !this.baseUrl) {
        return this.mockCreateSession(config)
      }

      const response = await fetch(`${this.baseUrl}/api/digital-human/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(config)
      })

      const data: DigitalHumanResponse = await response.json()
      
      if (data.success && data.data) {
        this.sessionId = data.data.sessionId
      }

      return data
    } catch (error) {
      console.error('创建数字人会话失败:', error)
      return {
        success: false,
        error: {
          code: 'CREATE_SESSION_FAILED',
          message: error instanceof Error ? error.message : '创建会话失败'
        }
      }
    }
  }

  /**
   * 获取RTMP拉流地址
   */
  async getRTMPStream(config?: Partial<DigitalHumanConfig>): Promise<string> {
    try {
      const sessionConfig: DigitalHumanConfig = {
        mode: 'RTMP',
        quality: 'high',
        resolution: '1080p',
        ...config
      }

      const response = await this.createSession(sessionConfig)
      
      if (response.success && response.data?.rtmp?.pullUrl) {
        console.log('获取RTMP拉流地址成功:', response.data.rtmp.pullUrl)
        return response.data.rtmp.pullUrl
      } else {
        throw new Error(response.error?.message || '获取RTMP流地址失败')
      }
    } catch (error) {
      console.error('获取RTMP流失败:', error)
      throw error
    }
  }

  /**
   * 获取流状态
   */
  async getStreamStatus(sessionId?: string): Promise<StreamStatus> {
    const id = sessionId || this.sessionId
    
    if (!id) {
      throw new Error('没有有效的会话ID')
    }

    try {
      // 开发环境返回模拟状态
      if (import.meta.env.DEV || !this.baseUrl) {
        return this.mockGetStreamStatus(id)
      }

      const response = await fetch(`${this.baseUrl}/api/digital-human/status/${id}`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      })

      const data = await response.json()
      return data
    } catch (error) {
      console.error('获取流状态失败:', error)
      throw error
    }
  }

  /**
   * 停止数字人会话
   */
  async stopSession(sessionId?: string): Promise<boolean> {
    const id = sessionId || this.sessionId
    
    if (!id) {
      console.warn('没有有效的会话ID')
      return true
    }

    try {
      // 开发环境直接返回成功
      if (import.meta.env.DEV || !this.baseUrl) {
        console.log('模拟停止会话:', id)
        this.sessionId = null
        return true
      }

      const response = await fetch(`${this.baseUrl}/api/digital-human/stop/${id}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      })

      const data = await response.json()
      
      if (data.success) {
        this.sessionId = null
      }

      return data.success
    } catch (error) {
      console.error('停止会话失败:', error)
      return false
    }
  }

  /**
   * 模拟创建会话（开发环境）
   */
  private mockCreateSession(config: DigitalHumanConfig): DigitalHumanResponse {
    console.log('模拟创建数字人会话:', config)
    
    // 使用您提供的真实RTMP地址
    const mockSessionId = 'mock_session_' + Date.now()
    
    const response: DigitalHumanResponse = {
      success: true,
      data: {
        rtmp: {
          pullUrl: 'rtmp://ns8.indexforce.com/home/<USER>'
        },
        sessionId: mockSessionId,
        status: 'active'
      }
    }

    // 根据配置返回不同的流地址
    if (config.mode === 'HLS') {
      response.data!.hls = {
        playlistUrl: 'https://live2d-pl.persona.baidu.com/i-rg4rc7idxxayz/test_api01.m3u8'
      }
    } else if (config.mode === 'WebRTC') {
      response.data!.webrtc = {
        sdp: 'mock_sdp_data',
        iceServers: []
      }
    }

    return response
  }

  /**
   * 模拟获取流状态（开发环境）
   */
  private mockGetStreamStatus(sessionId: string): StreamStatus {
    return {
      sessionId,
      status: 'active',
      startTime: Date.now() - 60000, // 1分钟前开始
      duration: 60,
      viewers: Math.floor(Math.random() * 100) + 1
    }
  }

  /**
   * 获取当前会话ID
   */
  getSessionId(): string | null {
    return this.sessionId
  }

  /**
   * 设置API配置
   */
  setConfig(baseUrl: string, apiKey: string): void {
    this.baseUrl = baseUrl
    this.apiKey = apiKey
  }
}

/**
 * 创建数字人API实例
 */
export function createDigitalHumanAPI(baseUrl?: string, apiKey?: string): DigitalHumanAPI {
  return new DigitalHumanAPI(baseUrl, apiKey)
}

/**
 * 默认API实例
 */
export const digitalHumanAPI = createDigitalHumanAPI()

/**
 * 便捷函数：获取RTMP流地址
 */
export async function getRTMPStreamUrl(config?: Partial<DigitalHumanConfig>): Promise<string> {
  return digitalHumanAPI.getRTMPStream(config)
}

/**
 * 便捷函数：获取HLS流地址
 */
export async function getHLSStreamUrl(config?: Partial<DigitalHumanConfig>): Promise<string> {
  const response = await digitalHumanAPI.createSession({
    mode: 'HLS',
    ...config
  })
  
  if (response.success && response.data?.hls?.playlistUrl) {
    return response.data.hls.playlistUrl
  } else {
    throw new Error(response.error?.message || '获取HLS流地址失败')
  }
}

/**
 * 检查数字人服务可用性
 */
export async function checkDigitalHumanService(): Promise<{
  available: boolean
  message: string
  supportedModes: string[]
}> {
  try {
    // 在开发环境总是返回可用
    if (import.meta.env.DEV) {
      return {
        available: true,
        message: '开发环境：数字人服务可用',
        supportedModes: ['RTMP', 'HLS', 'WebRTC']
      }
    }

    // 实际环境中检查服务状态
    const api = createDigitalHumanAPI()
    const baseUrl = api['baseUrl']
    
    if (!baseUrl) {
      return {
        available: false,
        message: '未配置数字人服务地址',
        supportedModes: []
      }
    }

    const response = await fetch(`${baseUrl}/api/health`)
    const data = await response.json()
    
    return {
      available: data.status === 'ok',
      message: data.message || '服务状态检查完成',
      supportedModes: data.supportedModes || ['RTMP']
    }
  } catch (error) {
    return {
      available: false,
      message: error instanceof Error ? error.message : '服务检查失败',
      supportedModes: []
    }
  }
}

/* 基础样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局变量 */
:root {
  /* 主色调 */
  --primary-color: #409eff;
  --primary-light: #79bbff;
  --primary-dark: #337ecc;
  
  /* 成功色 */
  --success-color: #67c23a;
  --success-light: #95d475;
  --success-dark: #529b2e;
  
  /* 警告色 */
  --warning-color: #e6a23c;
  --warning-light: #ebb563;
  --warning-dark: #b88230;
  
  /* 危险色 */
  --danger-color: #f56c6c;
  --danger-light: #f78989;
  --danger-dark: #c45656;
  
  /* 信息色 */
  --info-color: #909399;
  --info-light: #a6a9ad;
  --info-dark: #73767a;
  
  /* 文本色 */
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  /* 边框色 */
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  /* 背景色 */
  --background-base: #f5f7fa;
  --background-light: #fafafa;
  --background-white: #ffffff;
  
  /* 阴影 */
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  /* 圆角 */
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  --border-radius-round: 20px;
  --border-radius-circle: 50%;
}

/* 基础样式 - 移动端优先 */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-base, 14px);
  color: var(--text-primary);
  background-color: var(--background-base);
  line-height: var(--line-height-normal, 1.4);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;

  /* 移动端优化 */
  @media (max-width: 768px) {
    font-size: var(--font-size-sm, 12px);
    line-height: var(--line-height-relaxed, 1.6);
  }
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s;

  &:hover {
    color: var(--primary-light);
  }
}

/* 按钮基础样式 - 移动端优化 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: var(--mobile-touch-target, 44px);
  padding: var(--space-3, 12px) var(--space-4, 16px);
  border: 1px solid transparent;
  border-radius: var(--mobile-border-radius, 8px);
  font-size: var(--font-size-base, 14px);
  font-weight: 500;
  line-height: var(--line-height-normal, 1.4);
  cursor: pointer;
  transition: var(--transition-base, 0.2s ease);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;

  &:active {
    transform: scale(0.98);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
    transform: none;
  }

  /* 移动端样式调整 */
  @media (max-width: 768px) {
    padding: var(--space-4, 16px) var(--space-5, 20px);
    font-size: var(--font-size-lg, 16px);
  }
}

/* 卡片样式 - 移动端优化 */
.card {
  background: var(--background-white);
  border: 1px solid var(--border-lighter);
  border-radius: var(--mobile-border-radius, 8px);
  box-shadow: var(--shadow-base);
  overflow: hidden;
  margin-bottom: var(--mobile-margin, 12px);

  /* 移动端样式调整 */
  @media (max-width: 768px) {
    border-radius: var(--mobile-border-radius, 8px);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--space-3, 12px);
  }
}

/* 输入框样式 - 移动端优化 */
.input {
  width: 100%;
  min-height: var(--mobile-touch-target, 44px);
  padding: var(--space-3, 12px) var(--space-4, 16px);
  border: 1px solid var(--border-base);
  border-radius: var(--mobile-border-radius, 8px);
  font-size: var(--font-size-base, 14px);
  color: var(--text-primary);
  background-color: var(--background-white);
  transition: var(--transition-base, 0.2s ease);
  -webkit-appearance: none;

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  &::placeholder {
    color: var(--text-placeholder);
  }

  /* 移动端样式调整 */
  @media (max-width: 768px) {
    font-size: var(--font-size-lg, 16px); /* 防止iOS缩放 */
    padding: var(--space-4, 16px);
  }
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.hidden {
  display: none;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.overflow-hidden {
  overflow: hidden;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.select-none {
  user-select: none;
}

.transition-all {
  transition: all 0.3s ease;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }
.m-8 { margin: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }
.p-8 { padding: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }
.mt-6 { margin-top: 24px; }
.mt-8 { margin-top: 32px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }
.mb-6 { margin-bottom: 24px; }
.mb-8 { margin-bottom: 32px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }
.ml-5 { margin-left: 20px; }
.ml-6 { margin-left: 24px; }
.ml-8 { margin-left: 32px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }
.mr-5 { margin-right: 20px; }
.mr-6 { margin-right: 24px; }
.mr-8 { margin-right: 32px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 12px; }
.pt-4 { padding-top: 16px; }
.pt-5 { padding-top: 20px; }
.pt-6 { padding-top: 24px; }
.pt-8 { padding-top: 32px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 12px; }
.pb-4 { padding-bottom: 16px; }
.pb-5 { padding-bottom: 20px; }
.pb-6 { padding-bottom: 24px; }
.pb-8 { padding-bottom: 32px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 12px; }
.pl-4 { padding-left: 16px; }
.pl-5 { padding-left: 20px; }
.pl-6 { padding-left: 24px; }
.pl-8 { padding-left: 32px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 12px; }
.pr-4 { padding-right: 16px; }
.pr-5 { padding-right: 20px; }
.pr-6 { padding-right: 24px; }
.pr-8 { padding-right: 32px; }

/* 全局移动端样式 */
.is-mobile {
  .app-container {
    touch-action: pan-y;
  }
}

.is-desktop {
  .app-container {
    max-width: auto;
    margin: 0 auto;
  }
}

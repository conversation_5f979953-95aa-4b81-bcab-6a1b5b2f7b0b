/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

declare module 'element-plus'

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_BAIDU_APP_ID: string
  readonly VITE_BAIDU_API_KEY: string
  readonly VITE_BAIDU_SECRET_KEY: string
  readonly VITE_APP_TITLE: string
  readonly VITE_API_BASE_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
/**
 * 格式化时间戳
 * @param timestamp 时间戳
 */
export const formatTimestamp = (timestamp: number | null) => {
    if (!timestamp) return "未知"
    const date = new Date(timestamp * 1000)
    return date.toLocaleString()
  };

/**
 * 生成UUID v4
 * @returns {string} 生成的UUID字符串
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
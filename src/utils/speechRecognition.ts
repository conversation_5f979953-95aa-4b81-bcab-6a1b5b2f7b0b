/**
 * 语音识别工具类
 * 处理浏览器兼容性和权限管理
 */

// 语音识别配置接口
export interface SpeechRecognitionConfig {
  continuous?: boolean
  interimResults?: boolean
  lang?: string
  maxAlternatives?: number
}

// 语音识别结果接口
export interface SpeechRecognitionResult {
  transcript: string
  confidence: number
  isFinal: boolean
}

// 语音识别错误类型
export type SpeechRecognitionErrorType = 
  | 'no-speech'
  | 'audio-capture'
  | 'not-allowed'
  | 'network'
  | 'service-not-allowed'
  | 'aborted'
  | 'language-not-supported'
  | 'unknown'

// 语音识别事件回调
export interface SpeechRecognitionCallbacks {
  onStart?: () => void
  onEnd?: () => void
  onResult?: (result: SpeechRecognitionResult) => void
  onError?: (error: SpeechRecognitionErrorType, message: string) => void
  onNoMatch?: () => void
  onSpeechStart?: () => void
  onSpeechEnd?: () => void
}

// 全局类型声明
declare global {
  interface Window {
    SpeechRecognition: any
    webkitSpeechRecognition: any
  }
}

/**
 * 语音识别管理器
 */
export class SpeechRecognitionManager {
  private recognition: any| null = null
  private isRecording = false
  private callbacks: SpeechRecognitionCallbacks = {}

  constructor(config: SpeechRecognitionConfig = {}, callbacks: SpeechRecognitionCallbacks = {}) {
    this.callbacks = callbacks
    this.init(config)
  }

  /**
   * 检查浏览器是否支持语音识别
   */
  static isSupported(): boolean {
    return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
  }

  /**
   * 检查是否为移动设备
   */
  static isMobile(): boolean {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }

  /**
   * 请求麦克风权限
   */
  static async requestMicrophonePermission(): Promise<boolean> {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      // 立即停止流，我们只是为了获取权限
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      console.error('麦克风权限被拒绝:', error)
      return false
    }
  }

  /**
   * 获取错误消息
   */
  static getErrorMessage(error: SpeechRecognitionErrorType): string {
    const errorMessages: Record<SpeechRecognitionErrorType, string> = {
      'no-speech': '没有检测到语音，请重试',
      'audio-capture': '无法捕获音频，请检查麦克风',
      'not-allowed': '麦克风权限被拒绝，请允许使用麦克风',
      'network': '网络错误，请检查网络连接',
      'service-not-allowed': '语音识别服务不可用',
      'aborted': '语音识别被中断',
      'language-not-supported': '不支持的语言',
      'unknown': '未知错误'
    }
    return errorMessages[error] || '语音识别出现错误'
  }

  /**
   * 初始化语音识别
   */
  private init(config: SpeechRecognitionConfig): boolean {
    if (!SpeechRecognitionManager.isSupported()) {
      console.error('浏览器不支持语音识别')
      return false
    }

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      this.recognition = new SpeechRecognition()

      // 配置识别参数
      this.recognition.continuous = config.continuous ?? true
      this.recognition.interimResults = config.interimResults ?? true
      this.recognition.lang = config.lang ?? 'zh-CN'
      this.recognition.maxAlternatives = config.maxAlternatives ?? 1

      // 绑定事件处理器
      this.recognition.onstart = () => {
        this.isRecording = true
        this.callbacks.onStart?.()
      }

      this.recognition.onend = () => {
        this.isRecording = false
        this.callbacks.onEnd?.()
      }

      this.recognition.onresult = (event: any) => {
        this.handleResult(event)
      }

      this.recognition.onerror = (event: any) => {
        this.handleError(event)
      }

      this.recognition.onnomatch = () => {
        this.callbacks.onNoMatch?.()
      }

      this.recognition.onspeechstart = () => {
        this.callbacks.onSpeechStart?.()
      }

      this.recognition.onspeechend = () => {
        this.callbacks.onSpeechEnd?.()
      }

      return true
    } catch (error) {
      console.error('语音识别初始化失败:', error)
      return false
    }
  }

  /**
   * 处理识别结果
   */
  private handleResult(event: any): void {
    for (let i = event.resultIndex; i < event.results.length; i++) {
      const result = event.results[i]
      const transcript = result[0].transcript
      const confidence = result[0].confidence

      this.callbacks.onResult?.({
        transcript,
        confidence,
        isFinal: result.isFinal
      })
    }
  }

  /**
   * 处理错误
   */
  private handleError(event: any): void {
    this.isRecording = false
    const errorType = event.error as SpeechRecognitionErrorType
    const message = SpeechRecognitionManager.getErrorMessage(errorType)
    this.callbacks.onError?.(errorType, message)
  }

  /**
   * 开始录音
   */
  async start(): Promise<boolean> {
    if (!this.recognition) {
      console.error('语音识别未初始化')
      return false
    }

    if (this.isRecording) {
      console.warn('语音识别已在进行中')
      return false
    }

    try {
      // 请求麦克风权限
      const hasPermission = await SpeechRecognitionManager.requestMicrophonePermission()
      if (!hasPermission) {
        this.callbacks.onError?.('not-allowed', '需要麦克风权限才能使用语音识别功能')
        return false
      }

      this.recognition.start()
      return true
    } catch (error) {
      console.error('启动语音识别失败:', error)
      this.callbacks.onError?.('unknown', '启动语音识别失败')
      return false
    }
  }

  /**
   * 停止录音
   */
  stop(): void {
    if (this.recognition && this.isRecording) {
      this.recognition.stop()
    }
  }

  /**
   * 中止录音
   */
  abort(): void {
    if (this.recognition && this.isRecording) {
      this.recognition.abort()
    }
  }

  /**
   * 获取录音状态
   */
  getRecordingState(): boolean {
    return this.isRecording
  }

  /**
   * 更新配置
   */
  updateConfig(config: SpeechRecognitionConfig): void {
    if (!this.recognition) return

    if (config.continuous !== undefined) {
      this.recognition.continuous = config.continuous
    }
    if (config.interimResults !== undefined) {
      this.recognition.interimResults = config.interimResults
    }
    if (config.lang !== undefined) {
      this.recognition.lang = config.lang
    }
    if (config.maxAlternatives !== undefined) {
      this.recognition.maxAlternatives = config.maxAlternatives
    }
  }

  /**
   * 更新回调函数
   */
  updateCallbacks(callbacks: SpeechRecognitionCallbacks): void {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.recognition) {
      this.stop()
      this.recognition = null
    }
    this.callbacks = {}
  }
}

/**
 * 创建语音识别实例的便捷函数
 */
export function createSpeechRecognition(
  config: SpeechRecognitionConfig = {},
  callbacks: SpeechRecognitionCallbacks = {}
): SpeechRecognitionManager | null {
  if (!SpeechRecognitionManager.isSupported()) {
    console.warn('当前浏览器不支持语音识别')
    return null
  }

  return new SpeechRecognitionManager(config, callbacks)
}

/**
 * 检查语音识别可用性
 */
export function checkSpeechRecognitionAvailability(): {
  supported: boolean
  mobile: boolean
  https: boolean
  message: string
} {
  const supported = SpeechRecognitionManager.isSupported()
  const mobile = SpeechRecognitionManager.isMobile()
  const https = location.protocol === 'https:' || location.hostname === 'localhost'

  let message = ''
  if (!supported) {
    message = '当前浏览器不支持语音识别功能'
  } else if (!https) {
    message = '语音识别功能需要在HTTPS环境下使用'
  } else {
    message = '语音识别功能可用'
  }

  return {
    supported,
    mobile,
    https,
    message
  }
}

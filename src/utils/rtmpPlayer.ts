/**
 * RTMP流媒体播放工具类
 * 处理RTMP流的转换和播放
 */

// 流媒体配置接口
export interface StreamConfig {
  url: string
  autoplay?: boolean
  muted?: boolean
  controls?: boolean
  poster?: string
  fallbackUrls?: string[]
}

// 流媒体状态
export type StreamStatus = 'idle' | 'loading' | 'playing' | 'paused' | 'error' | 'ended'

// 流媒体事件回调
export interface StreamCallbacks {
  onStatusChange?: (status: StreamStatus) => void
  onLoad?: () => void
  onPlay?: () => void
  onPause?: () => void
  onError?: (error: string) => void
  onEnded?: () => void
  onTimeUpdate?: (currentTime: number, duration: number) => void
}

/**
 * 检查浏览器对各种流媒体格式的支持
 */
export class StreamFormatChecker {
  private static videoElement: HTMLVideoElement | null = null

  private static getVideoElement(): HTMLVideoElement {
    if (!this.videoElement) {
      this.videoElement = document.createElement('video')
    }
    return this.videoElement
  }

  /**
   * 检查HLS支持
   */
  static supportsHLS(): boolean {
    const video = this.getVideoElement()
    return video.canPlayType('application/vnd.apple.mpegurl') !== '' ||
           video.canPlayType('application/x-mpegURL') !== ''
  }

  /**
   * 检查DASH支持
   */
  static supportsDASH(): boolean {
    const video = this.getVideoElement()
    return video.canPlayType('application/dash+xml') !== ''
  }

  /**
   * 检查WebRTC支持
   */
  static supportsWebRTC(): boolean {
    return !!(window.RTCPeerConnection || window.webkitRTCPeerConnection || window.mozRTCPeerConnection)
  }

  /**
   * 检查MSE支持
   */
  static supportsMSE(): boolean {
    return !!(window.MediaSource || window.WebKitMediaSource)
  }

  /**
   * 获取支持的格式列表
   */
  static getSupportedFormats(): string[] {
    const formats: string[] = []
    
    if (this.supportsHLS()) formats.push('HLS')
    if (this.supportsDASH()) formats.push('DASH')
    if (this.supportsWebRTC()) formats.push('WebRTC')
    if (this.supportsMSE()) formats.push('MSE')
    
    return formats
  }
}

/**
 * RTMP URL转换器
 */
export class RTMPConverter {
  /**
   * 检查是否为RTMP URL
   */
  static isRTMPUrl(url: string): boolean {
    return url.toLowerCase().startsWith('rtmp://')
  }

  /**
   * 转换RTMP URL为HLS URL
   * 这是一个示例实现，实际项目中需要根据具体的流媒体服务进行调整
   */
  static convertToHLS(rtmpUrl: string): string {
    if (!this.isRTMPUrl(rtmpUrl)) {
      return rtmpUrl
    }

    // 示例转换逻辑：
    // rtmp://example.com/live/stream -> https://example.com/hls/stream.m3u8
    try {
      const url = new URL(rtmpUrl)
      const pathParts = url.pathname.split('/')
      const streamName = pathParts[pathParts.length - 1]
      
      // 构建HLS URL
      const hlsUrl = `https://${url.hostname}/hls/${streamName}.m3u8`
      return hlsUrl
    } catch (error) {
      console.error('RTMP URL转换失败:', error)
      return rtmpUrl
    }
  }

  /**
   * 转换RTMP URL为WebRTC URL
   */
  static convertToWebRTC(rtmpUrl: string): string {
    if (!this.isRTMPUrl(rtmpUrl)) {
      return rtmpUrl
    }

    try {
      const url = new URL(rtmpUrl)
      const pathParts = url.pathname.split('/')
      const streamName = pathParts[pathParts.length - 1]
      
      // 构建WebRTC URL
      const webrtcUrl = `wss://${url.hostname}/webrtc/${streamName}`
      return webrtcUrl
    } catch (error) {
      console.error('RTMP URL转换失败:', error)
      return rtmpUrl
    }
  }

  /**
   * 获取最佳播放URL
   */
  static getBestPlaybackUrl(rtmpUrl: string): string {
    if (!this.isRTMPUrl(rtmpUrl)) {
      return rtmpUrl
    }

    // 根据浏览器支持情况选择最佳格式
    if (StreamFormatChecker.supportsHLS()) {
      return this.convertToHLS(rtmpUrl)
    } else if (StreamFormatChecker.supportsWebRTC()) {
      return this.convertToWebRTC(rtmpUrl)
    } else {
      console.warn('浏览器不支持常见的流媒体格式，使用原始URL')
      return rtmpUrl
    }
  }
}

/**
 * 数字人视频流管理器
 */
export class DigitalHumanStreamManager {
  private videoElement: HTMLVideoElement | null = null
  private status: StreamStatus = 'idle'
  private callbacks: StreamCallbacks = {}
  private config: StreamConfig
  private retryCount = 0
  private maxRetries = 3

  constructor(config: StreamConfig, callbacks: StreamCallbacks = {}) {
    this.config = config
    this.callbacks = callbacks
  }

  /**
   * 初始化视频元素
   */
  init(videoElement: HTMLVideoElement): void {
    this.videoElement = videoElement
    this.setupVideoElement()
    this.bindEvents()
  }

  /**
   * 设置视频元素属性
   */
  private setupVideoElement(): void {
    if (!this.videoElement) return

    this.videoElement.autoplay = this.config.autoplay ?? true
    this.videoElement.muted = this.config.muted ?? true
    this.videoElement.controls = this.config.controls ?? false
    this.videoElement.playsInline = true
    
    // 移动端优化
    this.videoElement.setAttribute('webkit-playsinline', 'true')
    this.videoElement.setAttribute('x5-playsinline', 'true')
    this.videoElement.setAttribute('x5-video-player-type', 'h5')
    this.videoElement.setAttribute('x5-video-player-fullscreen', 'true')

    if (this.config.poster) {
      this.videoElement.poster = this.config.poster
    }
  }

  /**
   * 绑定视频事件
   */
  private bindEvents(): void {
    if (!this.videoElement) return

    this.videoElement.addEventListener('loadstart', () => {
      this.updateStatus('loading')
    })

    this.videoElement.addEventListener('loadeddata', () => {
      this.updateStatus('idle')
      this.callbacks.onLoad?.()
    })

    this.videoElement.addEventListener('play', () => {
      this.updateStatus('playing')
      this.callbacks.onPlay?.()
    })

    this.videoElement.addEventListener('pause', () => {
      this.updateStatus('paused')
      this.callbacks.onPause?.()
    })

    this.videoElement.addEventListener('ended', () => {
      this.updateStatus('ended')
      this.callbacks.onEnded?.()
    })

    this.videoElement.addEventListener('error', (event) => {
      this.handleError(event)
    })

    this.videoElement.addEventListener('timeupdate', () => {
      if (this.videoElement) {
        this.callbacks.onTimeUpdate?.(
          this.videoElement.currentTime,
          this.videoElement.duration || 0
        )
      }
    })
  }

  /**
   * 更新状态
   */
  private updateStatus(status: StreamStatus): void {
    this.status = status
    this.callbacks.onStatusChange?.(status)
  }

  /**
   * 处理错误
   */
  private handleError(event: Event): void {
    console.error('视频播放错误:', event)
    this.updateStatus('error')
    
    // 尝试重试
    if (this.retryCount < this.maxRetries) {
      this.retryCount++
      console.log(`尝试重新加载视频 (${this.retryCount}/${this.maxRetries})`)
      setTimeout(() => {
        this.load()
      }, 2000)
    } else {
      this.callbacks.onError?.('视频加载失败，请检查网络连接或视频源')
    }
  }

  /**
   * 加载视频
   */
  async load(): Promise<void> {
    if (!this.videoElement) {
      throw new Error('视频元素未初始化')
    }

    try {
      this.updateStatus('loading')
      
      // 获取最佳播放URL
      const playbackUrl = RTMPConverter.getBestPlaybackUrl(this.config.url)
      console.log('加载视频URL:', playbackUrl)
      
      this.videoElement.src = playbackUrl
      await this.videoElement.load()
      
      this.retryCount = 0 // 重置重试计数
    } catch (error) {
      console.error('视频加载失败:', error)
      this.handleError(new Event('error'))
    }
  }

  /**
   * 播放视频
   */
  async play(): Promise<void> {
    if (!this.videoElement) return

    try {
      await this.videoElement.play()
    } catch (error) {
      console.error('视频播放失败:', error)
      this.callbacks.onError?.('视频播放失败')
    }
  }

  /**
   * 暂停视频
   */
  pause(): void {
    if (this.videoElement) {
      this.videoElement.pause()
    }
  }

  /**
   * 停止视频
   */
  stop(): void {
    if (this.videoElement) {
      this.videoElement.pause()
      this.videoElement.currentTime = 0
    }
  }

  /**
   * 设置音量
   */
  setVolume(volume: number): void {
    if (this.videoElement) {
      this.videoElement.volume = Math.max(0, Math.min(1, volume))
    }
  }

  /**
   * 切换静音
   */
  toggleMute(): void {
    if (this.videoElement) {
      this.videoElement.muted = !this.videoElement.muted
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): StreamStatus {
    return this.status
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<StreamConfig>): void {
    this.config = { ...this.config, ...config }
    if (config.url && config.url !== this.config.url) {
      this.load()
    }
  }

  /**
   * 销毁实例
   */
  destroy(): void {
    if (this.videoElement) {
      this.stop()
      this.videoElement.src = ''
      this.videoElement = null
    }
    this.callbacks = {}
  }
}

/**
 * 创建数字人流媒体管理器的便捷函数
 */
export function createDigitalHumanStream(
  config: StreamConfig,
  callbacks: StreamCallbacks = {}
): DigitalHumanStreamManager {
  return new DigitalHumanStreamManager(config, callbacks)
}

/**
 * 检查流媒体播放能力
 */
export function checkStreamingCapability(): {
  rtmpSupported: boolean
  hlsSupported: boolean
  webrtcSupported: boolean
  recommendedFormat: string
  message: string
} {
  const hlsSupported = StreamFormatChecker.supportsHLS()
  const webrtcSupported = StreamFormatChecker.supportsWebRTC()
  const rtmpSupported = false // 现代浏览器不直接支持RTMP

  let recommendedFormat = 'HLS'
  let message = ''

  if (hlsSupported) {
    recommendedFormat = 'HLS'
    message = '推荐使用HLS格式进行流媒体播放'
  } else if (webrtcSupported) {
    recommendedFormat = 'WebRTC'
    message = '推荐使用WebRTC格式进行流媒体播放'
  } else {
    recommendedFormat = 'MP4'
    message = '当前浏览器流媒体支持有限，建议使用MP4格式'
  }

  return {
    rtmpSupported,
    hlsSupported,
    webrtcSupported,
    recommendedFormat,
    message
  }
}

/**
 * 音频处理工具类
 * 专门用于百度ASR的音频格式转换和处理
 */

// 音频配置接口
export interface AudioConfig {
  sampleRate: number
  channels: number
  bitsPerSample: number
}

// PCM音频数据接口
export interface PCMData {
  buffer: ArrayBuffer
  sampleRate: number
  channels: number
  bitsPerSample: number
  duration: number
}

/**
 * 音频处理器类
 */
export class AudioProcessor {
  private audioContext: AudioContext | null = null
  private workletNode: AudioWorkletNode | null = null

  constructor() {
    this.initAudioContext()
  }

  /**
   * 初始化音频上下文
   */
  private async initAudioContext(): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: 16000 // 百度ASR要求的采样率
      })
    } catch (error) {
      console.error('初始化音频上下文失败:', error)
    }
  }

  /**
   * 录制PCM音频数据
   */
  async recordPCM(
    stream: MediaStream, 
    config: AudioConfig = { sampleRate: 16000, channels: 1, bitsPerSample: 16 }
  ): Promise<PCMData> {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化')
    }

    return new Promise((resolve, reject) => {
      try {
        const source = this.audioContext!.createMediaStreamSource(stream)
        const processor = this.audioContext!.createScriptProcessor(4096, config.channels, config.channels)
        
        const pcmData: number[] = []
        const startTime = Date.now()

        processor.onaudioprocess = (event) => {
          const inputBuffer = event.inputBuffer
          const inputData = inputBuffer.getChannelData(0) // 获取第一个声道

          // 转换为16位PCM
          for (let i = 0; i < inputData.length; i++) {
            // 将浮点数转换为16位整数
            const sample = Math.max(-1, Math.min(1, inputData[i]))
            pcmData.push(sample < 0 ? sample * 0x8000 : sample * 0x7FFF)
          }
        }

        // 连接音频节点
        source.connect(processor)
        processor.connect(this.audioContext!.destination)

        // 设置停止录音的方法
        const stopRecording = () => {
          processor.disconnect()
          source.disconnect()

          // 转换为ArrayBuffer
          const buffer = new ArrayBuffer(pcmData.length * 2)
          const view = new DataView(buffer)
          
          for (let i = 0; i < pcmData.length; i++) {
            view.setInt16(i * 2, pcmData[i], true) // 小端序
          }

          const duration = (Date.now() - startTime) / 1000

          resolve({
            buffer,
            sampleRate: config.sampleRate,
            channels: config.channels,
            bitsPerSample: config.bitsPerSample,
            duration
          })
        }

        // 暴露停止方法
        (processor as any).stopRecording = stopRecording

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 将WebM/MP4音频转换为PCM
   */
  async convertToPCM(audioBlob: Blob, targetConfig: AudioConfig): Promise<PCMData> {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化')
    }

    try {
      // 读取音频文件
      const arrayBuffer = await audioBlob.arrayBuffer()
      
      // 解码音频数据
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer)
      
      // 重采样到目标采样率
      const resampledBuffer = await this.resampleAudio(audioBuffer, targetConfig.sampleRate)
      
      // 转换为PCM格式
      const pcmBuffer = this.audioBufferToPCM(resampledBuffer, targetConfig)
      
      return {
        buffer: pcmBuffer,
        sampleRate: targetConfig.sampleRate,
        channels: targetConfig.channels,
        bitsPerSample: targetConfig.bitsPerSample,
        duration: resampledBuffer.duration
      }
    } catch (error) {
      console.error('音频转换失败:', error)
      throw new Error('音频格式转换失败')
    }
  }

  /**
   * 重采样音频
   */
  private async resampleAudio(audioBuffer: AudioBuffer, targetSampleRate: number): Promise<AudioBuffer> {
    if (!this.audioContext) {
      throw new Error('音频上下文未初始化')
    }

    if (audioBuffer.sampleRate === targetSampleRate) {
      return audioBuffer
    }

    // 创建离线音频上下文进行重采样
    const offlineContext = new OfflineAudioContext(
      1, // 单声道
      audioBuffer.duration * targetSampleRate,
      targetSampleRate
    )

    // 创建音频源
    const source = offlineContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(offlineContext.destination)
    source.start()

    // 渲染音频
    return await offlineContext.startRendering()
  }

  /**
   * 将AudioBuffer转换为PCM格式
   */
  private audioBufferToPCM(audioBuffer: AudioBuffer, config: AudioConfig): ArrayBuffer {
    const channelData = audioBuffer.getChannelData(0) // 获取第一个声道
    const length = channelData.length
    const buffer = new ArrayBuffer(length * (config.bitsPerSample / 8))
    
    if (config.bitsPerSample === 16) {
      const view = new DataView(buffer)
      for (let i = 0; i < length; i++) {
        // 将浮点数转换为16位整数
        const sample = Math.max(-1, Math.min(1, channelData[i]))
        const intSample = sample < 0 ? sample * 0x8000 : sample * 0x7FFF
        view.setInt16(i * 2, intSample, true) // 小端序
      }
    } else if (config.bitsPerSample === 8) {
      const view = new Uint8Array(buffer)
      for (let i = 0; i < length; i++) {
        // 将浮点数转换为8位整数
        const sample = Math.max(-1, Math.min(1, channelData[i]))
        view[i] = (sample + 1) * 127.5
      }
    }

    return buffer
  }

  /**
   * 创建WAV文件头
   */
  createWAVHeader(pcmData: PCMData): ArrayBuffer {
    const buffer = new ArrayBuffer(44)
    const view = new DataView(buffer)

    // WAV文件头
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i))
      }
    }

    const dataLength = pcmData.buffer.byteLength
    const fileLength = dataLength + 44

    writeString(0, 'RIFF')
    view.setUint32(4, fileLength - 8, true)
    writeString(8, 'WAVE')
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true) // fmt chunk size
    view.setUint16(20, 1, true) // PCM format
    view.setUint16(22, pcmData.channels, true)
    view.setUint32(24, pcmData.sampleRate, true)
    view.setUint32(28, pcmData.sampleRate * pcmData.channels * (pcmData.bitsPerSample / 8), true)
    view.setUint16(32, pcmData.channels * (pcmData.bitsPerSample / 8), true)
    view.setUint16(34, pcmData.bitsPerSample, true)
    writeString(36, 'data')
    view.setUint32(40, dataLength, true)

    return buffer
  }

  /**
   * 创建完整的WAV文件
   */
  createWAVFile(pcmData: PCMData): Blob {
    const header = this.createWAVHeader(pcmData)
    return new Blob([header, pcmData.buffer], { type: 'audio/wav' })
  }

  /**
   * 计算音频音量
   */
  calculateVolume(audioData: Float32Array): number {
    let sum = 0
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i]
    }
    return Math.sqrt(sum / audioData.length)
  }

  /**
   * 检测静音
   */
  detectSilence(audioData: Float32Array, threshold: number = 0.01): boolean {
    const volume = this.calculateVolume(audioData)
    return volume < threshold
  }

  /**
   * 音频降噪（简单的高通滤波）
   */
  applyHighPassFilter(audioData: Float32Array, cutoffFreq: number, sampleRate: number): Float32Array {
    const rc = 1.0 / (cutoffFreq * 2 * Math.PI)
    const dt = 1.0 / sampleRate
    const alpha = rc / (rc + dt)
    
    const filtered = new Float32Array(audioData.length)
    filtered[0] = audioData[0]
    
    for (let i = 1; i < audioData.length; i++) {
      filtered[i] = alpha * (filtered[i - 1] + audioData[i] - audioData[i - 1])
    }
    
    return filtered
  }

  /**
   * 获取音频信息
   */
  getAudioInfo(audioBlob: Blob): Promise<{
    duration: number
    size: number
    type: string
  }> {
    return new Promise((resolve, reject) => {
      const audio = new Audio()
      const url = URL.createObjectURL(audioBlob)
      
      audio.onloadedmetadata = () => {
        URL.revokeObjectURL(url)
        resolve({
          duration: audio.duration,
          size: audioBlob.size,
          type: audioBlob.type
        })
      }
      
      audio.onerror = () => {
        URL.revokeObjectURL(url)
        reject(new Error('无法获取音频信息'))
      }
      
      audio.src = url
    })
  }

  /**
   * 销毁音频处理器
   */
  destroy(): void {
    if (this.workletNode) {
      this.workletNode.disconnect()
      this.workletNode = null
    }
    
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }
  }
}

/**
 * 创建音频处理器实例
 */
export function createAudioProcessor(): AudioProcessor {
  return new AudioProcessor()
}

/**
 * 检查音频处理支持
 */
export function checkAudioProcessingSupport(): {
  audioContext: boolean
  mediaRecorder: boolean
  offlineAudioContext: boolean
  supported: boolean
  message: string
} {
  const audioContextSupported = !!(window.AudioContext || (window as any).webkitAudioContext)
  const mediaRecorderSupported = typeof MediaRecorder !== 'undefined'
  const offlineAudioContextSupported = typeof OfflineAudioContext !== 'undefined'
  
  const supported = audioContextSupported && mediaRecorderSupported && offlineAudioContextSupported

  let message = ''
  if (!audioContextSupported) {
    message = '浏览器不支持AudioContext'
  } else if (!mediaRecorderSupported) {
    message = '浏览器不支持MediaRecorder'
  } else if (!offlineAudioContextSupported) {
    message = '浏览器不支持OfflineAudioContext'
  } else {
    message = '浏览器支持音频处理功能'
  }

  return {
    audioContext: audioContextSupported,
    mediaRecorder: mediaRecorderSupported,
    offlineAudioContext: offlineAudioContextSupported,
    supported,
    message
  }
}

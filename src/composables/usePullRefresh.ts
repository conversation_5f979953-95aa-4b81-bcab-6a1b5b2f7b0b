/**
 * 下拉刷新组合式函数
 */
import { ref, onMounted, onUnmounted } from 'vue'

export interface PullRefreshOptions {
  threshold?: number // 触发刷新的阈值
  maxDistance?: number // 最大下拉距离
  onRefresh?: () => Promise<void> // 刷新回调
  disabled?: boolean // 是否禁用
}

export function usePullRefresh(options: PullRefreshOptions = {}) {
  const {
    threshold = 60,
    maxDistance = 120,
    onRefresh,
    disabled = false
  } = options

  const isRefreshing = ref(false)
  const isPulling = ref(false)
  const pullDistance = ref(0)
  const canRefresh = ref(false)
  
  let startY = 0
  let currentY = 0
  let scrollTop = 0
  
  const container = ref<HTMLElement>()
  
  // 检查是否可以下拉
  const checkCanPull = () => {
    if (!container.value) return false
    return container.value.scrollTop === 0
  }
  
  // 处理触摸开始
  const handleTouchStart = (e: TouchEvent) => {
    if (disabled || isRefreshing.value) return
    
    scrollTop = container.value?.scrollTop || 0
    if (scrollTop > 0) return
    
    startY = e.touches[0].clientY
    isPulling.value = false
  }
  
  // 处理触摸移动
  const handleTouchMove = (e: TouchEvent) => {
    if (disabled || isRefreshing.value) return
    
    currentY = e.touches[0].clientY
    const deltaY = currentY - startY
    
    // 只有向下拉且在顶部时才处理
    if (deltaY > 0 && checkCanPull()) {
      e.preventDefault()
      isPulling.value = true
      
      // 计算下拉距离，使用阻尼效果
      const distance = Math.min(deltaY * 0.5, maxDistance)
      pullDistance.value = distance
      
      // 判断是否达到刷新阈值
      canRefresh.value = distance >= threshold
      
      // 更新容器样式
      if (container.value) {
        container.value.style.transform = `translateY(${distance}px)`
        container.value.style.transition = 'none'
      }
    }
  }
  
  // 处理触摸结束
  const handleTouchEnd = () => {
    if (disabled || isRefreshing.value || !isPulling.value) return
    
    isPulling.value = false
    
    if (container.value) {
      container.value.style.transition = 'transform 0.3s ease'
      
      if (canRefresh.value && onRefresh) {
        // 触发刷新
        isRefreshing.value = true
        container.value.style.transform = `translateY(${threshold}px)`
        
        onRefresh().finally(() => {
          isRefreshing.value = false
          canRefresh.value = false
          pullDistance.value = 0
          
          if (container.value) {
            container.value.style.transform = 'translateY(0)'
            setTimeout(() => {
              if (container.value) {
                container.value.style.transition = ''
              }
            }, 300)
          }
        })
      } else {
        // 回弹
        container.value.style.transform = 'translateY(0)'
        pullDistance.value = 0
        canRefresh.value = false
        
        setTimeout(() => {
          if (container.value) {
            container.value.style.transition = ''
          }
        }, 300)
      }
    }
  }
  
  // 绑定事件
  const bindEvents = () => {
    if (!container.value) return
    
    container.value.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.value.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.value.addEventListener('touchend', handleTouchEnd, { passive: true })
  }
  
  // 解绑事件
  const unbindEvents = () => {
    if (!container.value) return
    
    container.value.removeEventListener('touchstart', handleTouchStart)
    container.value.removeEventListener('touchmove', handleTouchMove)
    container.value.removeEventListener('touchend', handleTouchEnd)
  }
  
  // 手动触发刷新
  const refresh = async () => {
    if (disabled || isRefreshing.value || !onRefresh) return
    
    isRefreshing.value = true
    try {
      await onRefresh()
    } finally {
      isRefreshing.value = false
    }
  }
  
  onMounted(() => {
    bindEvents()
  })
  
  onUnmounted(() => {
    unbindEvents()
  })
  
  return {
    container,
    isRefreshing,
    isPulling,
    pullDistance,
    canRefresh,
    refresh,
    bindEvents,
    unbindEvents
  }
}

// 下拉刷新状态文本
export function usePullRefreshText() {
  const getStatusText = (isRefreshing: boolean, canRefresh: boolean, isPulling: boolean) => {
    if (isRefreshing) return '正在刷新...'
    if (canRefresh) return '释放即可刷新'
    if (isPulling) return '下拉可以刷新'
    return '下拉刷新'
  }
  
  const getStatusIcon = (isRefreshing: boolean, canRefresh: boolean, isPulling: boolean) => {
    if (isRefreshing) return '🔄'
    if (canRefresh) return '⬆️'
    if (isPulling) return '⬇️'
    return '⬇️'
  }
  
  return {
    getStatusText,
    getStatusIcon
  }
}

/**
 * 性能监控组合式函数
 */
import { ref, onMounted } from 'vue'

// 性能指标接口
export interface PerformanceMetrics {
  // 页面加载时间
  loadTime: number
  // 首次内容绘制
  fcp: number
  // 最大内容绘制
  lcp: number
  // 首次输入延迟
  fid: number
  // 累积布局偏移
  cls: number
  // 内存使用情况
  memory?: {
    used: number
    total: number
    limit: number
  }
}

// 性能监控
export function usePerformanceMonitor() {
  const metrics = ref<Partial<PerformanceMetrics>>({})
  const isSupported = ref(false)
  
  // 检查浏览器支持
  const checkSupport = () => {
    isSupported.value = 'performance' in window && 'PerformanceObserver' in window
  }
  
  // 获取页面加载时间
  const getLoadTime = () => {
    if (!performance.timing) return 0
    
    const { navigationStart, loadEventEnd } = performance.timing
    return loadEventEnd - navigationStart
  }
  
  // 获取首次内容绘制时间
  const getFCP = () => {
    return new Promise<number>((resolve) => {
      if (!isSupported.value) {
        resolve(0)
        return
      }
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
        
        if (fcpEntry) {
          resolve(fcpEntry.startTime)
          observer.disconnect()
        }
      })
      
      observer.observe({ entryTypes: ['paint'] })
      
      // 超时处理
      setTimeout(() => {
        observer.disconnect()
        resolve(0)
      }, 5000)
    })
  }
  
  // 获取最大内容绘制时间
  const getLCP = () => {
    return new Promise<number>((resolve) => {
      if (!isSupported.value) {
        resolve(0)
        return
      }
      
      let lcpValue = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1]
        lcpValue = lastEntry.startTime
      })
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
      
      // 页面隐藏时停止观察
      const stopObserving = () => {
        observer.disconnect()
        resolve(lcpValue)
      }
      
      document.addEventListener('visibilitychange', stopObserving, { once: true })
      
      // 超时处理
      setTimeout(stopObserving, 10000)
    })
  }
  
  // 获取累积布局偏移
  const getCLS = () => {
    return new Promise<number>((resolve) => {
      if (!isSupported.value) {
        resolve(0)
        return
      }
      
      let clsValue = 0
      
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
          }
        })
      })
      
      observer.observe({ entryTypes: ['layout-shift'] })
      
      // 页面隐藏时停止观察
      const stopObserving = () => {
        observer.disconnect()
        resolve(clsValue)
      }
      
      document.addEventListener('visibilitychange', stopObserving, { once: true })
      
      // 超时处理
      setTimeout(stopObserving, 10000)
    })
  }
  
  // 获取内存使用情况
  const getMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      }
    }
    return undefined
  }
  
  // 收集所有性能指标
  const collectMetrics = async () => {
    checkSupport()
    
    const [fcp, lcp, fid] = await Promise.all([
      getFCP(),
      getLCP(),
      getCLS()
    ])
    
    metrics.value = {
      loadTime: getLoadTime(),
      fcp,
      lcp,
      fid,
      memory: getMemoryUsage()
    }
    
    return metrics.value
  }
  
  // 性能评分
  const getPerformanceScore = (metrics: Partial<PerformanceMetrics>) => {
    let score = 100
    
    // FCP 评分 (0-2.5s: 100, 2.5-4s: 50, >4s: 0)
    if (metrics.fcp) {
      if (metrics.fcp > 4000) score -= 25
      else if (metrics.fcp > 2500) score -= 12.5
    }
    
    // LCP 评分 (0-2.5s: 100, 2.5-4s: 50, >4s: 0)
    if (metrics.lcp) {
      if (metrics.lcp > 4000) score -= 25
      else if (metrics.lcp > 2500) score -= 12.5
    }
    
    // FID 评分 (0-100ms: 100, 100-300ms: 50, >300ms: 0)
    if (metrics.fid) {
      if (metrics.fid > 300) score -= 25
      else if (metrics.fid > 100) score -= 12.5
    }
    
    // CLS 评分 (0-0.1: 100, 0.1-0.25: 50, >0.25: 0)
    if (metrics.cls) {
      if (metrics.cls > 0.25) score -= 25
      else if (metrics.cls > 0.1) score -= 12.5
    }
    
    return Math.max(0, score)
  }
  
  return {
    metrics,
    isSupported,
    collectMetrics,
    getPerformanceScore,
    getLoadTime,
    getFCP,
    getLCP,
    getCLS,
    getMemoryUsage
  }
}

// 资源加载监控
export function useResourceMonitor() {
  const resources = ref<PerformanceResourceTiming[]>([])
  
  const getResourceTiming = () => {
    if (!performance.getEntriesByType) return []
    
    return performance.getEntriesByType('resource') as PerformanceResourceTiming[]
  }
  
  const analyzeResources = () => {
    const resourceList = getResourceTiming()
    resources.value = resourceList
    
    const analysis = {
      total: resourceList.length,
      images: resourceList.filter(r => r.initiatorType === 'img').length,
      scripts: resourceList.filter(r => r.initiatorType === 'script').length,
      stylesheets: resourceList.filter(r => r.initiatorType === 'link').length,
      totalSize: resourceList.reduce((sum, r) => sum + (r.transferSize || 0), 0),
      slowResources: resourceList.filter(r => r.duration > 1000)
    }
    
    return analysis
  }
  
  return {
    resources,
    getResourceTiming,
    analyzeResources
  }
}

// 网络状况监控
export function useNetworkMonitor() {
  const networkInfo = ref<any>(null)
  const isOnline = ref(navigator.onLine)
  
  const getNetworkInfo = () => {
    if ('connection' in navigator) {
      networkInfo.value = (navigator as any).connection
    }
    return networkInfo.value
  }
  
  const updateOnlineStatus = () => {
    isOnline.value = navigator.onLine
  }
  
  onMounted(() => {
    getNetworkInfo()
    
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // 监听网络变化
    if (networkInfo.value) {
      networkInfo.value.addEventListener('change', getNetworkInfo)
    }
  })
  
  return {
    networkInfo,
    isOnline,
    getNetworkInfo
  }
}

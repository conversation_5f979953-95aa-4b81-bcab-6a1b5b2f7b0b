
// BRTC SDK 初始化结果接口
export interface BRTCSDKInitResult {
  code: number;
  message: string;
}


// BRTC SDK 类声明
export declare class BRTCSDK {
  constructor();

  // 初始化  连接
  init(): Promise<BRTCSDKInitResult>;
}

// 全局声明
declare global {
  interface Window {
    BRTCSDK: typeof BRTCSDK;
    globalBRTCInstance: BRTCSDK | null;
  }
}

// Vue 全局属性声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $BRTCSDK: typeof BRTCSDK | null;
    $createBRTCSDKInstance: (params: BRTCSDKParams) => BRTCSDK;
  }
}

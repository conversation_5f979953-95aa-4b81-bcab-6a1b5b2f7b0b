{"name": "rtmp-proxy-server", "version": "1.0.0", "description": "RTMP流代理服务器，将RTMP流转换为HLS格式", "main": "rtmp-proxy.js", "scripts": {"start": "node rtmp-proxy.js", "dev": "nodemon rtmp-proxy.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["rtmp", "hls", "ffmpeg", "streaming", "proxy"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}
@echo off
echo 启动RTMP代理服务器...
echo.

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

REM 检查FFmpeg是否安装
ffmpeg -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到FFmpeg，请先安装FFmpeg
    echo 下载地址: https://ffmpeg.org/download.html
    echo 或使用chocolatey安装: choco install ffmpeg
    pause
    exit /b 1
)

REM 检查依赖是否安装
if not exist node_modules (
    echo 安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo 依赖安装失败
        pause
        exit /b 1
    )
)

echo 启动代理服务器...
echo 服务器地址: http://localhost:3000
echo 使用示例: http://localhost:3000/stream?url=rtmp://your-rtmp-url
echo.
echo 按 Ctrl+C 停止服务器
echo.

npm start

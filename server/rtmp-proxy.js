/**
 * RTMP流代理服务器
 * 使用Node.js和FFmpeg将RTMP流转换为HLS格式
 * 
 * 使用方法:
 * 1. 安装依赖: npm install express cors child_process fs-extra
 * 2. 安装FFmpeg: https://ffmpeg.org/download.html
 * 3. 运行: node rtmp-proxy.js
 * 4. 访问: http://localhost:3000/stream?url=rtmp://your-rtmp-url
 */

const express = require('express');
const cors = require('cors');
const { spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const app = express();
const port = process.env.PORT || 3000;

// 启用CORS
app.use(cors());

// 创建输出目录
const outputDir = path.join(__dirname, 'streams');
fs.ensureDirSync(outputDir);

// 存储活动流进程
const activeStreams = new Map();

// 清理函数
function cleanupStream(streamId) {
  const process = activeStreams.get(streamId);
  if (process) {
    console.log(`停止流 ${streamId}`);
    process.kill('SIGTERM');
    activeStreams.delete(streamId);
    
    // 清理文件
    const streamDir = path.join(outputDir, streamId);
    try {
      fs.removeSync(streamDir);
    } catch (err) {
      console.error(`清理文件失败: ${err.message}`);
    }
  }
}

// 定期清理不活跃的流
setInterval(() => {
  const now = Date.now();
  activeStreams.forEach((process, streamId) => {
    const lastAccess = process.lastAccess || 0;
    if (now - lastAccess > 5 * 60 * 1000) { // 5分钟不活跃
      cleanupStream(streamId);
    }
  });
}, 60 * 1000); // 每分钟检查一次

// 处理流请求
app.get('/stream', async (req, res) => {
  const rtmpUrl = req.query.url;
  
  if (!rtmpUrl) {
    return res.status(400).send('缺少RTMP URL参数');
  }
  
  // 生成唯一的流ID
  const streamId = Buffer.from(rtmpUrl).toString('base64').replace(/[+/=]/g, '');
  const streamDir = path.join(outputDir, streamId);
  const playlistPath = path.join(streamDir, 'playlist.m3u8');
  
  // 检查流是否已经在运行
  if (activeStreams.has(streamId)) {
    console.log(`使用现有流 ${streamId}`);
    activeStreams.get(streamId).lastAccess = Date.now();
    return res.redirect(`/streams/${streamId}/playlist.m3u8`);
  }
  
  // 创建流目录
  fs.ensureDirSync(streamDir);
  
  try {
    console.log(`开始处理RTMP流: ${rtmpUrl}`);
    
    // 启动FFmpeg进程
    const ffmpeg = spawn('ffmpeg', [
      '-i', rtmpUrl,
      '-c:v', 'copy',
      '-c:a', 'aac',
      '-f', 'hls',
      '-hls_time', '2',
      '-hls_list_size', '6',
      '-hls_flags', 'delete_segments',
      '-hls_segment_filename', path.join(streamDir, 'segment_%03d.ts'),
      playlistPath
    ]);
    
    // 存储进程信息
    ffmpeg.lastAccess = Date.now();
    activeStreams.set(streamId, ffmpeg);
    
    // 处理FFmpeg输出
    ffmpeg.stdout.on('data', (data) => {
      console.log(`FFmpeg输出: ${data}`);
    });
    
    ffmpeg.stderr.on('data', (data) => {
      console.log(`FFmpeg日志: ${data}`);
    });
    
    ffmpeg.on('close', (code) => {
      console.log(`FFmpeg进程退出，代码: ${code}`);
      activeStreams.delete(streamId);
    });
    
    // 等待播放列表文件生成
    let attempts = 0;
    const maxAttempts = 30;
    
    while (attempts < maxAttempts) {
      if (fs.existsSync(playlistPath)) {
        console.log(`播放列表已生成: ${playlistPath}`);
        return res.redirect(`/streams/${streamId}/playlist.m3u8`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
      attempts++;
    }
    
    return res.status(500).send('生成播放列表超时');
    
  } catch (error) {
    console.error(`处理流失败: ${error.message}`);
    cleanupStream(streamId);
    return res.status(500).send(`处理流失败: ${error.message}`);
  }
});

// 提供HLS文件
app.use('/streams', express.static(outputDir));

// 健康检查
app.get('/health', (req, res) => {
  res.send({
    status: 'ok',
    activeStreams: Array.from(activeStreams.keys()),
    uptime: process.uptime()
  });
});

// 停止特定流
app.get('/stop-stream', (req, res) => {
  const streamId = req.query.id;
  
  if (!streamId) {
    return res.status(400).send('缺少流ID参数');
  }
  
  if (activeStreams.has(streamId)) {
    cleanupStream(streamId);
    res.send({ success: true, message: `流 ${streamId} 已停止` });
  } else {
    res.status(404).send({ success: false, message: `流 ${streamId} 不存在` });
  }
});

// 获取流信息
app.get('/stream-info', (req, res) => {
  const streamId = req.query.id;
  
  if (!streamId) {
    return res.status(400).send('缺少流ID参数');
  }
  
  if (activeStreams.has(streamId)) {
    const process = activeStreams.get(streamId);
    res.send({
      id: streamId,
      active: true,
      lastAccess: process.lastAccess,
      uptime: (Date.now() - process.startTime) / 1000
    });
  } else {
    res.status(404).send({ success: false, message: `流 ${streamId} 不存在` });
  }
});

// 列出所有活动流
app.get('/streams-list', (req, res) => {
  const streams = Array.from(activeStreams.keys()).map(id => {
    const process = activeStreams.get(id);
    return {
      id,
      lastAccess: process.lastAccess,
      uptime: (Date.now() - (process.startTime || Date.now())) / 1000
    };
  });
  
  res.send({ streams });
});

// 启动服务器
app.listen(port, () => {
  console.log(`RTMP代理服务器运行在 http://localhost:${port}`);
  console.log(`使用示例: http://localhost:${port}/stream?url=rtmp://example.com/live/stream`);
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  
  // 停止所有流
  activeStreams.forEach((process, streamId) => {
    cleanupStream(streamId);
  });
  
  process.exit(0);
});

/**
 * 使用示例:
 * 
 * 1. 启动代理服务器:
 *    node rtmp-proxy.js
 * 
 * 2. 在前端使用:
 *    const rtmpUrl = 'rtmp://ns8.indexforce.com/home/<USER>';
 *    const proxyUrl = `http://localhost:3000/stream?url=${encodeURIComponent(rtmpUrl)}`;
 *    videoElement.src = proxyUrl;
 * 
 * 3. 停止特定流:
 *    http://localhost:3000/stop-stream?id=流ID
 * 
 * 4. 查看所有活动流:
 *    http://localhost:3000/streams-list
 */
